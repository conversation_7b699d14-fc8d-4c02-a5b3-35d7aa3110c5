<?php

namespace admin\controllers;

use common\service\memberCancel\ResumeCancelDataCleanService;
use common\service\memberCancel\ResumeCancelSearchService;
use common\service\memberCancel\ResumeCancelService;
use Yii;
use yii\base\Exception;

/**
 * 求职者注销管理控制器
 * 运营后台用于管理求职者注销申请
 */
class ResumeCancelController extends BaseAdminController
{
    /**
     * 获取注销申请列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetList()
    {
        try {
            $params = Yii::$app->request->get();
            $data   = ResumeCancelSearchService::getList($params);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取注销申请详情
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetDetail()
    {
        try {
            $cancelLogId = Yii::$app->request->get('cancelLogId');
            if (empty($cancelLogId)) {
                throw new Exception('注销日志ID不能为空');
            }

            $data = ResumeCancelSearchService::getDetail($cancelLogId);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取统计数据
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetStatistics()
    {
        try {
            $params = Yii::$app->request->get();
            $data   = ResumeCancelSearchService::getStatistics($params);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取筛选选项
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetFilterOptions()
    {
        try {
            $data = ResumeCancelSearchService::getFilterOptions();

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 人工执行注销（运营后台操作）
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionManualCancel()
    {
        $cancelLogId = Yii::$app->request->post('cancelLogId');
        if (empty($cancelLogId)) {
            return $this->fail('注销日志ID不能为空');
        }
        try {
            // 开启事务
            $transaction = Yii::$app->db->beginTransaction();

            $adminId = Yii::$app->user->id;

            // 执行注销操作
            $dataCleanService = new ResumeCancelDataCleanService();
            $result           = $dataCleanService->executeCancel($cancelLogId, $adminId);

            $transaction->commit();

            return $this->success($result, '注销操作执行成功');
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 主动进入冷静期接口
     * 允许用户主动触发进入冷静期状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEnterCooldown()
    {
        try {
            $params = Yii::$app->request->post();

            // 参数验证
            $resumeId = $params['resumeId'] ?? '';
            if (empty($resumeId)) {
                throw new Exception('简历ID不能为空');
            }

            $cancelReasonType = $params['cancelReasonType'] ?? '';
            if (empty($cancelReasonType)) {
                throw new Exception('注销原因类型不能为空');
            }

            // 调用服务层方法
            $resumeCancelService = new ResumeCancelService();
            $result = $resumeCancelService->applyCancel([
                'resumeId' => $resumeId,
                'cancelReasonType' => $cancelReasonType,
                'cancelReasonDetail' => $params['cancelReasonDetail'] ?? '',
                'smsCode' => $params['smsCode'] ?? '',
                'ip' => Yii::$app->request->getUserIP(),
            ]);

            return $this->success($result, '已成功进入冷静期');
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 取消冷静期接口（运营后台操作）
     * 允许运营后台直接取消用户的冷静期状态（撤回注销申请）
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCancelCooldown()
    {
        try {
            $params = Yii::$app->request->post();

            // 参数验证
            $cancelLogId = $params['cancelLogId'] ?? '';
            if (empty($cancelLogId)) {
                throw new Exception('注销日志ID不能为空');
            }

            // 开启事务
            $transaction = Yii::$app->db->beginTransaction();

            $adminId = Yii::$app->user->id;

            // 调用服务层方法进行撤回操作
            $resumeCancelService = new ResumeCancelService();
            $result = $resumeCancelService->adminWithdrawCancel($cancelLogId, $adminId);

            $transaction->commit();

            return $this->success($result, '冷静期已成功取消');
        } catch (Exception $e) {
            if (isset($transaction)) {
                $transaction->rollBack();
            }
            return $this->fail($e->getMessage());
        }
    }

}