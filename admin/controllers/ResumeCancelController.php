<?php

namespace admin\controllers;

use common\base\models\BaseResumeCancelLog;
use common\service\memberCancel\ResumeCancelDataCleanService;
use common\service\memberCancel\ResumeCancelSearchService;
use common\service\memberCancel\ResumeCancelService;
use Yii;
use yii\base\Exception;

/**
 * 求职者注销管理控制器
 * 运营后台用于管理求职者注销申请
 */
class ResumeCancelController extends BaseAdminController
{
    /**
     * 获取注销申请列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetList()
    {
        try {
            $params = Yii::$app->request->get();
            $data   = ResumeCancelSearchService::getList($params);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取注销申请详情
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetDetail()
    {
        try {
            $cancelLogId = Yii::$app->request->get('cancelLogId');
            if (empty($cancelLogId)) {
                throw new Exception('注销日志ID不能为空');
            }

            $data = ResumeCancelSearchService::getDetail($cancelLogId);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取统计数据
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetStatistics()
    {
        try {
            $params = Yii::$app->request->get();
            $data   = ResumeCancelSearchService::getStatistics($params);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取筛选选项
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetFilterOptions()
    {
        try {
            $data = ResumeCancelSearchService::getFilterOptions();

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 人工执行注销（运营后台操作）
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionManualCancel()
    {
        $cancelLogId = Yii::$app->request->post('cancelLogId');
        if (empty($cancelLogId)) {
            return $this->fail('注销日志ID不能为空');
        }
        try {
            // 开启事务
            $transaction = Yii::$app->db->beginTransaction();

            $adminId = Yii::$app->user->id;

            // 执行注销操作
            $dataCleanService = new ResumeCancelDataCleanService();
            $result           = $dataCleanService->executeCancel($cancelLogId, $adminId);

            $transaction->commit();

            return $this->success($result, '注销操作执行成功');
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 主动进入冷静期接口
     * 允许用户主动触发进入冷静期状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEnterCooldown()
    {
        try {
            $params = Yii::$app->request->post();

            // 参数验证
            $resumeId = $params['resumeId'] ?? '';
            if (empty($resumeId)) {
                throw new Exception('简历ID不能为空');
            }

            $cancelReasonType = $params['cancelReasonType'] ?? '';
            if (empty($cancelReasonType)) {
                throw new Exception('注销原因类型不能为空');
            }

            // 调用服务层方法
            $resumeCancelService = new ResumeCancelService();
            $result = $resumeCancelService->applyCancel([
                'resumeId' => $resumeId,
                'cancelReasonType' => $cancelReasonType,
                'cancelReasonDetail' => $params['cancelReasonDetail'] ?? '',
                'smsCode' => $params['smsCode'] ?? '',
                'ip' => Yii::$app->request->getUserIP(),
            ]);

            return $this->success($result, '已成功进入冷静期');
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 取消冷静期接口（运营后台操作）
     * 允许运营后台直接取消用户的冷静期状态（撤回注销申请）
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCancelCooldown()
    {
        try {
            $params = Yii::$app->request->post();

            // 参数验证
            $cancelLogId = $params['cancelLogId'] ?? '';
            if (empty($cancelLogId)) {
                throw new Exception('注销日志ID不能为空');
            }

            // 开启事务
            $transaction = Yii::$app->db->beginTransaction();

            // 直接通过注销日志ID进行撤回操作，跳过token验证
            $result = $this->withdrawCancelByLogId($cancelLogId);

            $transaction->commit();

            return $this->success($result, '冷静期已成功取消');
        } catch (Exception $e) {
            if (isset($transaction)) {
                $transaction->rollBack();
            }
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 通过注销日志ID撤回注销申请（运营后台专用）
     * @param int $cancelLogId 注销日志ID
     * @return array 撤回结果
     * @throws Exception
     */
    private function withdrawCancelByLogId($cancelLogId)
    {
        // 查找注销申请记录
        $cancelLog = BaseResumeCancelLog::findOne($cancelLogId);
        if (!$cancelLog) {
            throw new Exception('注销申请记录不存在');
        }

        // 检查状态是否为申请中
        if ($cancelLog->status != BaseResumeCancelLog::STATUS_APPLYING) {
            throw new Exception('注销申请状态异常，无法撤回');
        }

        // 运营后台操作可以在冷静期结束后撤回，所以不检查冷静期时间

        // 更新注销日志状态为已撤回
        $cancelLog->status = BaseResumeCancelLog::STATUS_WITHDRAWN;
        $cancelLog->withdraw_time = date('Y-m-d H:i:s');
        $cancelLog->admin_id = Yii::$app->user->id; // 记录操作的管理员ID
        if (!$cancelLog->save()) {
            throw new Exception('更新注销日志失败：' . $cancelLog->getFirstErrorsMessage());
        }

        // 恢复预处理操作
        $dataCleanService = new ResumeCancelDataCleanService();
        $dataCleanService->restorePreCancelOperations($cancelLog);

        return [
            'success' => true,
            'message' => '注销申请已成功撤回',
            'withdrawTime' => $cancelLog->withdraw_time,
            'adminId' => $cancelLog->admin_id,
        ];
    }

}