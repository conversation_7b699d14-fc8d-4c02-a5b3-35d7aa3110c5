# 求职者注销账号功能开发文档

**版本**: V2.7.2
**创建时间**: 2024-01-17
**最后更新**: 2024-01-17
**文档状态**: 服务层已实现

## 修改记录

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| V2.7.2 | 2024-01-17 | 初始版本，完成需求分析和架构设计 | 开发团队 |
| V2.7.2.1 | 2024-01-17 | 完成服务层实现，更新数据库设计为双状态系统 | AI助手 |
| V2.7.2.2 | 2024-01-17 | 简化数据库结构，移除冗余字段，优化服务层接口 | AI助手 |

## 实现状态

- ✅ **数据库设计**: member表新增cancel_status字段
- ✅ **BaseMember类扩展**: 新增注销状态常量和便捷方法
- ✅ **服务层实现**: ResumeCancelService和ResumeCancelDataCleanService
- ✅ **预处理逻辑**: 申请时立即执行，撤回时恢复
- 🔄 **人才库查询修改**: 需要按文档修改相关查询逻辑
- ⏳ **控制器层**: 待开发API接口
- ⏳ **定时任务**: 待开发冷静期处理任务
- ⏳ **前端集成**: 待开发前端页面

## 数据库结构优化说明

### V2.7.2.2 版本优化内容

#### 1. resume_cancel_log 表简化
**移除字段**：
- `operator_type` - 操作类型字段
- `operator_id` - 操作人ID字段
- `operator_name` - 操作人姓名字段

**保留字段**：
- `admin_id` - 运营操作时记录管理员ID，系统操作时为0

**优化理由**：
- 简化操作记录逻辑，通过admin_id区分系统操作和人工操作
- 减少字段冗余，降低维护复杂度
- 保持核心功能完整性

#### 2. resume_cancel_snapshot 表简化
**移除字段**：
- `member_data_json` - 会员数据快照
- `related_data_json` - 关联数据快照
- `setting_data_json` - 设置数据快照

**保留字段**：
- `resume_data_json` - 简历数据快照（核心数据）
- 基本联系信息字段（mobile、email、username、name）

**优化理由**：
- 聚焦核心数据保护，简化快照逻辑
- 减少存储空间占用
- 降低快照创建和恢复的复杂度

#### 3. resume_cancel_restriction 表字段确认
**当前字段**：
- 基本字段：id、add_time、update_time
- 限制字段：mobile、mobile_code、email
- 关联字段：cancel_log_id
- 时间字段：restriction_end_time

**说明**：该表结构保持稳定，字段设计合理

## 1. 功能概述

本功能为求职者提供账号注销服务，用户可以主动申请注销账号，系统将根据用户状态和数据情况进行相应处理。支持用户多次申请和撤回注销，包含7天冷静期机制和180天重新注册限制。

## 2. 数据库设计

### 2.1 member表扩展

#### 2.1.1 新增cancel_status字段

为member表新增专门的注销状态字段，与现有status字段独立工作：

```sql
-- 添加cancel_status字段
ALTER TABLE `member`
ADD COLUMN `cancel_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '注销状态：0未申请注销，9注销中（冷静期），1注销成功（已完成）'
AFTER `company_member_type`;

-- 添加索引
ALTER TABLE `member`
ADD INDEX `idx_cancel_status` (`cancel_status`) USING BTREE;

ALTER TABLE `member`
ADD INDEX `idx_member_cancel_status` (`id`, `cancel_status`) USING BTREE;
```

#### 2.1.2 BaseMember类常量定义

```php
// 在BaseMember类中新增常量
class BaseMember extends Member implements IdentityInterface
{
    // 现有状态保持不变
    const STATUS_DELETE = 0;           // 删除
    const STATUS_ACTIVE = 1;           // 正常
    const STATUS_ILLEGAL = -1;         // 禁止登陆
    const STATUS_NO_COOPERATION = -2;  // 非合作帐号
    const STATUS_AUDIT = 9;            // 等待审核

    // 原有求职者注销状态（保留兼容性）
    const STATUS_RESUME_CANCELING = 10;    // 求职者注销中
    const STATUS_RESUME_CANCELED = 11;     // 求职者已注销

    // 新增注销状态常量（cancel_status字段使用）
    const CANCEL_STATUS_NORMAL = 0;      // 未申请注销
    const CANCEL_STATUS_CANCELING = 9;   // 注销中（冷静期）
    const CANCEL_STATUS_CANCELED = 1;    // 注销成功（已完成）

    // 扩展状态名称映射
    const COMPANY_STATUS_LIST = [
        self::STATUS_ACTIVE => '正常',
        self::STATUS_DELETE => '删除',
        self::STATUS_AUDIT => '等待审核',
        self::STATUS_ILLEGAL => '已禁用',
        self::STATUS_NO_COOPERATION => '非合作帐号',
        self::STATUS_RESUME_CANCELING => '注销中',
        self::STATUS_RESUME_CANCELED => '已注销',
    ];

    // 注销状态名称映射
    const CANCEL_STATUS_LIST = [
        self::CANCEL_STATUS_NORMAL => '正常',
        self::CANCEL_STATUS_CANCELING => '注销中',
        self::CANCEL_STATUS_CANCELED => '已注销',
    ];
}
```

### 2.2 新增数据表

#### 2.2.1 求职者注销日志表 (resume_cancel_log)

**用途**：记录用户的每次注销申请、撤回、完成等操作日志，支持用户多次申请和取消。

**关键字段**：
- `member_id`：求职者会员ID
- `resume_id`：简历ID
- `cancel_reason_type`：注销原因类型（1已找到工作，2不满意服务，3注册多个账号，4其他）
- `cancel_reason_detail`：注销原因详细说明（选择其他时填写）
- `apply_time`：申请时间
- `cooldown_end_time`：冷静期结束时间（申请时间+7天）
- `status`：状态（1申请中，2已撤回，3已完成）
- `ip`：IP地址（支持IPv4和IPv6格式）

**业务规则**：
- 支持同一用户多次申请和撤回
- 同一时间只能有一条status=STATUS_APPLYING（申请中）的记录
- 每次申请都会创建新的日志记录

#### 2.2.2 求职者数据快照表 (resume_cancel_snapshot)

**用途**：在用户申请注销时保存完整的用户数据快照，用于数据保留和恢复。

**关键字段**：
- `cancel_log_id`：关联注销日志ID（一对一关系）
- `mobile`、`mobile_code`、`email`、`username`、`name`：关键联系信息单独存储
- `resume_data_json`：简历完整数据快照（JSON格式，text类型）

**业务规则**：
- 每次注销申请创建一个快照
- 快照数据永久保留，不随注销清理
- 关键联系信息单独存储便于查询
- 简化为只保存简历数据快照，减少存储复杂度

#### 2.2.3 注销限制表 (resume_cancel_restriction)

**用途**：记录已注销用户的手机号和邮箱，实现180天内不可重新注册的限制。

**关键字段**：
- `mobile`、`mobile_code`：手机号和区号
- `email`：邮箱地址
- `cancel_log_id`：关联注销日志ID
- `restriction_end_time`：限制结束时间（注销时间+180天）

**业务规则**：
- 注销完成后创建限制记录
- 限制期内使用相同手机号或邮箱注册将被拒绝
- 限制期结束后自动失效（可通过定时任务清理过期记录）
- 简化字段设计，移除了member_id和resume_id字段

### 2.3 业务常量定义

#### 2.3.1 注销日志表常量 (BaseResumeCancelLog)
```php
class BaseResumeCancelLog extends BaseActiveRecord
{
      // 注销原因类型常量
    const CANCEL_REASON_TYPE_FOUND_JOB        = 1;          // 已找到新工作，以后不再打算找工作
    const CANCEL_REASON_TYPE_CHANGE_MOBILE    = 2;          // 换手机号了，重新注册
    const CANCEL_REASON_TYPE_WRONG_ACCOUNT    = 3;          // 注册企业账号，误操作为个人账号
    const CANCEL_REASON_TYPE_MULTIPLE_ACCOUNT = 4;          // 已有多个账号，想注销一个
    const CANCEL_REASON_TYPE_PRIVACY_CONCERN  = 5;          // 担心隐私泄露
    const CANCEL_REASON_TYPE_NO_INVITATION    = 6;          // 不想接收到邀约邮件
    const CANCEL_REASON_TYPE_OTHER            = 99;          // 其他原因

    const CANCEL_REASON_TYPE_LIST = [
        self::CANCEL_REASON_TYPE_FOUND_JOB        => '已找到新工作，以后不再打算找工作',
        self::CANCEL_REASON_TYPE_CHANGE_MOBILE    => '换手机号了，重新注册',
        self::CANCEL_REASON_TYPE_WRONG_ACCOUNT    => '注册企业账号，误操作为个人账号',
        self::CANCEL_REASON_TYPE_MULTIPLE_ACCOUNT => '已有多个账号，想注销一个',
        self::CANCEL_REASON_TYPE_PRIVACY_CONCERN  => '担心隐私泄露',
        self::CANCEL_REASON_TYPE_NO_INVITATION    => '不想接收到邀约邮件',
        self::CANCEL_REASON_TYPE_OTHER            => '其他原因',
    ];

    // 注销申请状态常量
    const STATUS_APPLYING  = 1;    // 申请中
    const STATUS_WITHDRAWN = 2;   // 已撤回
    const STATUS_COMPLETED = 3;   // 已完成

    const STATUS_LIST = [
        self::STATUS_APPLYING  => '申请中',
        self::STATUS_WITHDRAWN => '已撤回',
        self::STATUS_COMPLETED => '已完成',
    ];

}
```

#### 2.3.2 注销限制表常量 (BaseResumeCancelRestriction)
```php
class BaseResumeCancelRestriction extends BaseActiveRecord
{
    // 限制状态常量
    const STATUS_ACTIVE = 1;      // 生效中
    const STATUS_EXPIRED = 2;     // 已失效

    const STATUS_LIST = [
        self::STATUS_ACTIVE => '生效中',
        self::STATUS_EXPIRED => '已失效',
    ];
}
```

### 2.4 状态转换逻辑

#### 2.4.1 双状态系统设计

**member.status字段**：控制账号基本状态（正常、删除、禁用等）
**member.cancel_status字段**：专门控制注销流程状态

#### 2.4.2 用户状态转换
```
正常用户 (cancel_status=0)
    ↓ 申请注销（立即执行预处理）
注销中 (cancel_status=9)
    ↓ 撤回申请（恢复部分设置）
正常用户 (cancel_status=0)
    ↓ 重新申请注销
注销中 (cancel_status=9)
    ↓ 冷静期结束（执行最终注销）
已注销 (cancel_status=1)
```

#### 2.4.3 日志状态转换
```
申请注销 → log.status=BaseResumeCancelLog::STATUS_APPLYING (申请中)
撤回申请 → log.status=BaseResumeCancelLog::STATUS_WITHDRAWN (已撤回)
冷静期结束 → log.status=BaseResumeCancelLog::STATUS_COMPLETED (已完成)
```

#### 2.4.4 状态关联关系
| member.cancel_status | resume_cancel_log.status | 业务状态描述 | 用户可执行操作 |
|---------------------|-------------------------|-------------|---------------|
| 0 (未申请注销) | 无记录 | 正常用户，从未申请注销 | 可申请注销 |
| 0 (未申请注销) | 2 (已撤回) | 曾申请注销但已撤回 | 可重新申请注销 |
| 9 (注销中) | 1 (申请中) | 正在冷静期内 | 可撤回注销 |
| 1 (注销成功) | 3 (已完成) | 注销流程已完成 | 无法操作 |

### 2.5 数据快照JSON结构

#### 2.5.1 member_data_json 结构示例
```json
{
    "basic_info": {
        "id": 123,
        "add_time": "2023-01-01 12:00:00",
        "update_time": "2024-01-01 10:00:00",
        "status": 1,
        "type": 1,
        "username": "user123",
        "password": "hashed_password",
        "avatar": "/uploads/avatar/123.jpg",
        "last_login_time": "2024-01-01 10:00:00",
        "last_login_ip": 123456789,
        "email_register_status": 1,
        "source_type": 1,
        "last_active_time": "2024-01-01 10:00:00"
    }
}
```

#### 2.5.2 resume_data_json 结构示例
```json
{
    "basic_info": {
        "id": 456,
        "member_id": 123,
        "audit_status": 1,
        "name": "张三",
        "gender": 1,
        "birthday": "1990-01-01",
        "mobile": "13800138000",
        "email": "<EMAIL>",
        "education": 3,
        "work_experience": 5,
        "complete": 85,
        "vip_type": 0
    }
}
```

#### 2.5.3 related_data_json 结构示例
```json
{
    "education_list": [
        {
            "id": 789,
            "resume_id": 456,
            "school_name": "北京大学",
            "major": "计算机科学",
            "degree": 3,
            "start_time": "2008-09-01",
            "end_time": "2012-06-30"
        }
    ],
    "work_experience_list": [
        {
            "id": 101,
            "resume_id": 456,
            "company_name": "某科技公司",
            "position": "软件工程师",
            "start_time": "2012-07-01",
            "end_time": "2020-12-31"
        }
    ],
    "project_experience_list": [],
    "skill_certificate_list": [],
    "resume_file_list": []
}
```

#### 2.5.4 setting_data_json 结构示例
```json
{
    "member_bind_list": [
        {
            "id": 111,
            "member_id": 123,
            "type": 1,
            "openid": "wx_openid_123",
            "unionid": "wx_unionid_456",
            "status": 1
        }
    ],
    "subscription_settings": {
        "job_subscribe": true,
        "message_notify": true,
        "email_notify": false,
        "sms_notify": true
    },
    "privacy_settings": {
        "mobile_visible": false,
        "email_visible": true,
        "resume_public": true
    }
}
```

## 3. 业务流程设计

### 3.1 注销申请流程（已实现）
1. 用户发起注销申请
2. 系统检查是否有进行中的申请
3. 验证短信验证码
4. 创建注销日志记录
5. **立即执行预处理操作**：
   - 解绑微信/小程序绑定关系
   - 退出人才库（更新is_resume_library字段）
   - 关闭职位订阅
   - 关闭消息通知
   - 更新cancel_status为"注销中"(9)
6. 发送申请短信通知
7. 进入7天冷静期

**重要变更**：预处理操作在申请时立即执行，不等到冷静期结束。

### 3.2 冷静期撤回流程（已实现）
1. 用户登录时检测到注销状态（cancel_status=9）
2. 用户选择撤回注销
3. 更新日志状态为"已撤回"
4. 恢复用户cancel_status为"正常"(0)
5. 重新评估人才库资格（调用ResumeLibraryService）
6. **注意**：微信绑定和消息通知设置不自动恢复，需用户手动重新设置

### 3.3 定时任务处理流程
1. 第6天发送提醒短信
2. 第7天执行正式注销：
   - 创建数据快照（用于备份和恢复）
   - 执行最终数据清理（清除个人敏感数据）
   - 更新cancel_status为"已注销"(1)
   - 更新日志状态为"已完成"
   - 添加180天限制记录
   - 发送注销成功短信

**重要说明**：预处理操作已在申请时执行，冷静期结束只执行最终的数据清理和状态更新。

## 4. 服务层架构设计（已实现）

### 4.1 核心服务类

#### ResumeCancelService（注销申请服务）
**职责**：处理注销申请相关的业务逻辑

**公共方法**：
- `checkEligibility($resumeId)` - 检查注销资格
- `checkActiveJobServices($resumeId)` - 检查生效中的求职服务
- `sendCancelSms($mobile, $mobileCode)` - 发送验证码
- `validateSmsCode($mobile, $code, $mobileCode)` - 验证验证码
- `applyCancel($params)` - 提交注销申请（已集成预处理逻辑）
- `withdrawCancel($memberId)` - 撤回注销申请（新增）
- `getCancelConfig()` - 获取注销配置信息

**私有方法**：
- `getCancelDialogContent()` - 获取注销弹窗内容
- `getCancelAgreementUrl()` - 获取注销协议URL
- `sendApplyNotification($mobile, $mobileCode)` - 发送申请成功通知

#### ResumeCancelDataCleanService（注销执行服务）
**职责**：处理注销相关的数据操作，包括预处理和最终清理

**公共方法**：
- `executeCancel($cancelLogId, $adminId)` - 执行最终注销操作（简化参数）
- `executePreCancelOperations($resume, $member)` - **新增**：执行预处理操作（申请时调用）
- `restorePreCancelOperations($memberId, $resumeId)` - **新增**：恢复预处理操作（撤回时调用）

**私有方法**：
- `validateCancelLog($cancelLogId)` - 验证注销申请状态
- `getResumeInfo($resumeId)` - 获取简历信息
- `getMemberInfo($memberId)` - 获取会员信息
- `createDataSnapshot($cancelLog)` - 创建数据快照
- `executeDataClean($resume, $member)` - 执行数据清理
- `updateMemberStatus($member)` - 更新用户状态（已修改为使用cancel_status）
- `updateCancelLogStatus($cancelLog, $adminId)` - 更新注销日志状态（简化参数）
- `addRestrictionRecord($member, $cancelLogId)` - 添加180天限制记录
- `sendCancelSuccessNotification($member)` - 发送注销成功通知
- `logOperation($memberId, $result, $errorMessage)` - 记录操作日志（简化参数）

**新增预处理相关方法**：
- `unbindMiniProgram($memberId)` - 解绑微信/小程序
- `exitResumeLibrary($resumeId)` - 退出人才库
- `disableJobSubscription($resumeId)` - 关闭职位订阅
- `disableMessageNotification($memberId)` - 关闭消息通知
- `updateMemberCancelStatus($memberId, $cancelStatus)` - 更新注销状态
- `reevaluateResumeLibrary($resumeId)` - 重新评估人才库资格

#### ResumeCancelSnapshotService（快照服务）
**职责**：处理数据快照的创建和恢复

**公共方法**：
- `createSnapshot($resumeId, $cancelLogId)` - 创建数据快照（复用checkResumeInfo方法）
- `restoreFromSnapshot($snapshotId)` - 从快照恢复数据

#### ResumeCancelRestrictionService（限制服务）
**职责**：处理180天重新注册限制

**公共方法**：
- `addRestriction($mobile, $email, $mobileCode)` - 添加180天限制
- `checkRestriction($mobile, $email)` - 检查限制状态

### 4.2 服务调用关系

#### 注销申请流程（已更新）
```
Controller → ResumeCancelService::applyCancel()
           ├── 创建注销日志记录
           ├── ResumeCancelDataCleanService::executePreCancelOperations()
           │   ├── 解绑微信/小程序
           │   ├── 退出人才库
           │   ├── 关闭职位订阅
           │   ├── 关闭消息通知
           │   └── 更新cancel_status=9
           └── 发送申请成功通知
```

#### 撤回注销流程（新增）
```
Controller → ResumeCancelService::withdrawCancel()
           ├── 更新日志状态为已撤回
           └── ResumeCancelDataCleanService::restorePreCancelOperations()
               ├── 更新cancel_status=0
               └── 重新评估人才库资格
```

#### 注销执行流程（定时任务）
```
TimerController → ResumeCancelDataCleanService::executeCancel()
                ├── ResumeCancelSnapshotService::createSnapshot()
                ├── 执行最终数据清理
                ├── 更新cancel_status=1
                ├── ResumeCancelRestrictionService::addRestriction()
                └── SmsQueue（发送通知）
```

#### 注销执行流程（人工操作）
```
AdminController → ResumeCancelDataCleanService::executeCancel()
                ├── ResumeCancelSnapshotService::createSnapshot()
                ├── 执行最终数据清理
                ├── 更新cancel_status=1
                ├── ResumeCancelRestrictionService::addRestriction()
                └── （不发送短信通知）
```

### 4.3 设计原则

#### 单一职责原则（已更新）
- **ResumeCancelService**：处理申请和撤回的业务逻辑，集成预处理调用
- **ResumeCancelDataCleanService**：处理预处理、恢复和最终清理的数据操作
- **ResumeCancelSnapshotService**：只处理数据快照相关操作
- **ResumeCancelRestrictionService**：只处理限制相关操作

#### 依赖关系清晰（已更新）
- 申请服务依赖执行服务的预处理方法
- 执行服务依赖快照服务和限制服务
- 所有服务都不处理事务，事务由控制器层处理

#### 操作溯源（已简化）
- 人工操作通过admin_id字段记录操作人
- 系统自动操作admin_id为0
- 完整的操作日志记录

#### 状态管理原则（新增）
- 使用双状态系统：status（基本状态）+ cancel_status（注销状态）
- 预处理操作立即执行，提供更好的用户体验
- 支持撤回操作，增强用户控制权

## 5. 索引设计说明

### 4.1 查询场景分析
- **用户状态查询**：根据member_id查询当前注销状态
- **定时任务查询**：根据cooldown_end_time查询需要处理的记录
- **限制检查查询**：根据mobile/email查询是否在限制期内
- **运营后台查询**：根据时间范围、状态等条件查询日志

### 4.2 索引设计原则
- 单字段索引：针对高频查询字段
- 复合索引：针对组合查询条件
- 时间索引：便于定时任务和范围查询
- 唯一索引：确保数据一致性

## 5. 数据保留策略

### 5.1 永久保留数据
- 数据快照：完整保留用户数据快照
- 注销日志：保留所有注销操作记录
- 业务关联数据：投递、收藏、邀约等业务数据

### 5.2 清理数据
- 个人敏感信息：姓名、手机号、邮箱、身份证等
- 简历内容：教育经历、工作经历、项目经历等
- 个人设置：订阅设置、隐私设置、第三方绑定等

### 5.3 脱敏处理
- 姓名替换为"用户已注销"
- 头像替换为默认已注销头像
- 联系方式完全清除

## 6. 安全和合规考虑

### 6.1 数据安全
- 快照数据加密存储
- 访问权限严格控制
- 操作日志完整记录

### 6.2 法律合规
- 符合数据保护法规要求
- 保留必要的业务数据
- 提供数据恢复机制（通过快照）

## 7. 后续开发计划

### 7.1 Phase 1: 数据库和基础服务
- 创建数据库表结构
- 开发基础Model类：
  - `common/base/models/BaseResumeCancelLog.php`
  - `common/base/models/BaseResumeCancelSnapshot.php`
  - `common/base/models/BaseResumeCancelRestriction.php`
  - `common/models/ResumeCancelLog.php`
  - `common/models/ResumeCancelSnapshot.php`
  - `common/models/ResumeCancelRestriction.php`
- 开发数据快照服务

### 7.2 Phase 2: 求职者端功能
- PC端注销申请流程
- 冷静期撤回功能
- 登录拦截逻辑

### 7.3 Phase 3: 定时任务和数据处理
- 定时任务控制器
- 数据清理服务
- 短信发送逻辑

### 7.4 Phase 4: 运营后台功能
- 用户状态筛选
- 注销日志管理
- 数据快照查看

### 7.5 Phase 5: 单位端展示适配
- 已注销用户标识
- 简历访问限制
- 直聊功能限制

## 8. API接口文档

### 8.1 获取注销配置接口

**接口地址**：`GET /api/person/resume/get-cancel-config`

**接口描述**：获取注销页面的配置信息，包括注销原因列表、弹窗内容等

**请求参数**：无

**返回示例**：
```json
{
  "msg": "",
  "result": 1,
  "data": {
    "cancelReasonList": [
      {"value": 1, "label": "已找到工作"},
      {"value": 2, "label": "不满意服务"},
      {"value": 3, "label": "注册多个账号"},
      {"value": 4, "label": "其他"}
    ],
    "cancelDialogContent": "<p>注销提示内容...</p>",
    "cancelAgreementUrl": "/agreement/cancel"
  }
}
```

### 8.2 检查注销资格接口

**接口地址**：`GET /api/person/resume/check-cancel-eligibility`

**接口描述**：检查用户是否具备注销资格，包括是否有生效中的求职服务

**请求参数**：无

**返回示例**：
```json
{
  "msg": "",
  "result": 1,
  "data": {
    "hasActiveServices": true,
    "serviceWarning": "您当前有生效中的求职服务，注销后将无法继续使用"
  }
}
```

**错误返回示例**：
```json
{
  "msg": "您已有进行中的注销申请，请勿重复提交",
  "result": 0,
  "data": null
}
```

### 8.3 发送注销验证码接口

**接口地址**：`POST /api/person/resume/send-cancel-sms`

**接口描述**：发送求职者注销账号的短信验证码

**请求参数**：
```
ticket,String,是,captcha_ticket,-,腾讯云滑块验证码ticket
randstr,String,是,captcha_randstr,-,腾讯云滑块验证码randstr
```

**返回示例**：
```json
{
  "msg": "验证码发送成功",
  "result": 1,
  "data": []
}
```

**错误返回示例**：
```json
{
  "msg": "请完成滑块验证",
  "result": 0,
  "data": null
}
```

### 8.2 提交注销申请接口

**接口地址**：`POST /api/person/resume/apply-cancel`

**接口描述**：提交求职者账号注销申请

**请求参数**：
```
cancelReasonType,Number,是,1,-,注销原因类型：1已找到工作，2不满意服务，3注册多个账号，4其他
cancelReasonDetail,String,否,其他原因,-,注销原因详细说明（选择其他时必填）
smsCode,String,是,123456,-,短信验证码
```

**返回示例**：
```json
{
  "msg": "注销申请提交成功",
  "result": 1,
  "data": {
    "cancelLogId": 123,
    "cooldownEndTime": "2024-01-08 10:00:00"
  }
}
```

**错误返回示例**：
```json
{
  "msg": "请输入短信验证码",
  "result": 0,
  "data": null
}
```

### 8.5 接口调用流程

1. **页面初始化**：
   - 调用 `/api/person/resume/get-cancel-config` 获取配置信息
   - 调用 `/api/person/resume/check-cancel-eligibility` 检查注销资格

2. **发送验证码**：
   - 前端先完成腾讯云滑块验证
   - 调用 `/api/person/resume/send-cancel-sms` 发送验证码
   - 系统发送包含6位数字验证码的短信

3. **提交注销申请**：
   - 用户填写注销原因和验证码
   - 调用 `/api/person/resume/apply-cancel` 提交申请
   - 系统验证验证码并创建注销申请记录

### 8.6 错误码说明

| 错误信息 | 说明 | 处理建议 |
|---------|------|----------|
| 请完成滑块验证 | 滑块验证码参数缺失 | 重新完成滑块验证 |
| 验证参数错误 | randstr参数错误 | 检查滑块验证码参数 |
| 用户不存在 | 用户账号异常 | 重新登录 |
| 用户未绑定手机号 | 账号未绑定手机号 | 先绑定手机号 |
| 请选择注销原因 | 注销原因类型参数缺失 | 选择注销原因 |
| 注销原因类型无效 | 注销原因类型值错误 | 使用正确的原因类型值 |
| 请填写详细的注销原因 | 选择其他原因时未填写详情 | 填写详细原因 |
| 请输入短信验证码 | 验证码参数缺失 | 输入收到的验证码 |
| 验证码错误或已过期 | 验证码验证失败 | 重新获取验证码 |

### 8.7 前端集成说明

#### 8.7.1 页面流程设计
1. **注销申请页面**：
   - 显示注销原因选择（单选）
   - 其他原因时显示文本输入框
   - 集成腾讯云滑块验证组件
   - 短信验证码发送和输入区域

2. **验证码发送**：
   - 发送按钮60秒倒计时防重复
   - 滑块验证成功后才能发送
   - 显示发送成功/失败提示

3. **表单验证**：
   - 注销原因必选验证
   - 其他原因时详情必填验证
   - 验证码格式验证（6位数字）

#### 8.7.2 状态管理
- 发送验证码后启动60秒倒计时
- 验证码输入框实时验证格式
- 提交按钮根据表单完整性控制可用状态

#### 8.7.3 错误处理
- 网络错误统一提示
- 业务错误根据返回信息精确提示
- 验证码错误时清空输入框并重新聚焦

## 9. 定时任务和后台管理集成

### 9.1 定时任务控制器示例

**文件：`timer/controllers/ResumeCancelTaskController.php`**

```php
<?php

namespace timer\controllers;

use common\service\memberCancel\ResumeCancelDataCleanService;
use common\base\models\BaseResumeCancelLog;
use Yii;

/**
 * 求职者注销定时任务控制器
 * php timer_yii resume-cancel-task/execute-pending
 */
class ResumeCancelTaskController extends BaseTimerController
{
    /**
     * 执行到期的注销申请
     */
    public function actionExecutePending()
    {
        // 查找冷静期已结束的注销申请
        $pendingLogs = BaseResumeCancelLog::find()
            ->where([
                'status' => BaseResumeCancelLog::STATUS_APPLYING
            ])
            ->andWhere(['<=', 'cooldown_end_time', date('Y-m-d H:i:s')])
            ->limit(50) // 每次处理50个
            ->all();

        self::log('找到待处理的注销申请：' . count($pendingLogs) . '个');

        foreach ($pendingLogs as $cancelLog) {
            $this->executeCancel($cancelLog->id);
        }
    }

    /**
     * 执行单个注销申请
     */
    private function executeCancel($cancelLogId)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeCancelDataCleanService();
            $result = $service->executeCancel($cancelLogId, 'system');

            $transaction->commit();
            self::log("注销执行成功 - 日志ID: {$cancelLogId}");

        } catch (\Exception $e) {
            $transaction->rollBack();
            self::log("注销执行失败 - 日志ID: {$cancelLogId}, 错误: " . $e->getMessage());
        }
    }
}
```

### 9.2 后台管理控制器示例

**文件：`admin/controllers/person/ResumeCancelController.php`**

```php
/**
 * 手动执行注销
 */
public function actionManualExecute()
{
    $cancelLogId = Yii::$app->request->post('cancelLogId');
    $adminId = Yii::$app->user->id;

    $transaction = Yii::$app->db->beginTransaction();
    try {
        $service = new ResumeCancelDataCleanService();
        $result = $service->executeCancel($cancelLogId, $adminId);

        $transaction->commit();
        return $this->success($result, '注销执行成功');

    } catch (\Exception $e) {
        $transaction->rollBack();
        return $this->fail($e->getMessage());
    }
}
```

### 9.3 事务处理说明

#### 控制器层事务处理的优势：
- ✅ **统一的事务管理**：所有数据库操作在同一个事务中
- ✅ **更好的错误处理**：可以在控制器层统一处理事务回滚和错误响应
- ✅ **服务层职责单一**：服务层专注业务逻辑，不处理事务
- ✅ **便于测试**：服务层方法更容易进行单元测试

#### 注意事项：
- 事务应该尽可能短，避免长时间锁定
- 在事务中不要执行耗时操作（如发送短信）
- 短信发送等通知操作在事务提交后执行

## 10. 人才库查询逻辑修改

### 10.1 修改目标
确保注销中（cancel_status=9）和已注销（cancel_status=1）的用户不会出现在人才库推荐中。

### 10.2 需要修改的文件

#### 10.2.1 人才库搜索服务
**文件**: `common/service/commonResume/SearchService.php`
**修改位置**: `getCommonQuery()` 方法

```php
// 在现有查询条件基础上添加
$common_query->andWhere(['m.cancel_status' => BaseMember::CANCEL_STATUS_NORMAL]);
```

#### 10.2.2 人岗匹配服务
**文件**: `common/service/match/JobToPersonService.php`
**修改位置**: `getQuery()` 方法

```php
// 添加member表关联和过滤条件
->innerJoin(['m' => BaseMember::tableName()], 'r.member_id=m.id')
->andWhere(['m.cancel_status' => BaseMember::CANCEL_STATUS_NORMAL])
```

#### 10.2.3 人才库基础查询
**文件**: `common/base/models/BaseResumeLibrary.php`
**修改位置**: 所有涉及人才库查询的方法

```php
// 确保所有查询都包含cancel_status过滤条件
->andWhere(['m.cancel_status' => BaseMember::CANCEL_STATUS_NORMAL])
```

#### 10.2.4 运营后台人才管理
**文件**: `admin/models/Member.php`
**修改位置**: `getSearchPersonList()` 方法

```php
// 添加注销状态筛选选项
if (isset($searchData['cancelStatus']) && $searchData['cancelStatus'] !== '') {
    $query->andWhere(['m.cancel_status' => $searchData['cancelStatus']]);
} else {
    // 默认不显示已注销用户
    $query->andWhere(['!=', 'm.cancel_status', BaseMember::CANCEL_STATUS_CANCELED]);
}
```

### 10.3 性能优化建议

#### 10.3.1 索引使用
- 优先使用 `idx_cancel_status` 索引进行过滤
- 在复杂查询中，将cancel_status条件放在WHERE子句前面

#### 10.3.2 查询优化
```php
// 推荐：优先使用cancel_status过滤
$query->andWhere(['m.cancel_status' => BaseMember::CANCEL_STATUS_NORMAL]);

// 不推荐：复杂的子查询
$query->andWhere(['not exists',
    (new Query())->from('resume_cancel_log')->where('...')
]);
```

### 10.4 测试验证要点
1. 验证注销中用户不出现在搜索结果中
2. 验证已注销用户不出现在推荐列表中
3. 验证撤回注销后用户能正常出现在人才库中
4. 验证查询性能没有明显下降

### 10.5 缓存更新策略
- 修改查询条件后，需要清理相关缓存
- 人才库相关的Redis缓存需要重新生成
- 搜索索引可能需要更新

## 11. BaseMember类扩展方法

### 11.1 新增便捷方法

#### 11.1.1 状态检查方法
```php
/**
 * 检查用户是否处于注销状态
 */
public static function isCanceling($memberId)
{
    $cancelStatus = self::findOneVal(['id' => $memberId], 'cancel_status');
    return $cancelStatus == self::CANCEL_STATUS_CANCELING;
}

/**
 * 检查用户是否已注销
 */
public static function isCanceled($memberId)
{
    $cancelStatus = self::findOneVal(['id' => $memberId], 'cancel_status');
    return $cancelStatus == self::CANCEL_STATUS_CANCELED;
}

/**
 * 检查用户是否可以正常使用
 */
public static function canUseNormally($memberId)
{
    $cancelStatus = self::findOneVal(['id' => $memberId], 'cancel_status');
    return $cancelStatus == self::CANCEL_STATUS_NORMAL;
}
```

#### 11.1.2 状态管理方法
```php
/**
 * 更新用户注销状态
 */
public static function updateCancelStatus($memberId, $cancelStatus)
{
    $member = self::findOne($memberId);
    if (!$member) {
        throw new Exception('用户不存在');
    }

    $member->cancel_status = $cancelStatus;
    $member->update_time = date('Y-m-d H:i:s');

    if (!$member->save()) {
        throw new Exception($member->getFirstErrorsMessage());
    }

    return true;
}

/**
 * 获取注销状态文本
 */
public static function getCancelStatusText($cancelStatus)
{
    return self::CANCEL_STATUS_LIST[$cancelStatus] ?? '未知状态';
}
```

### 11.2 使用示例

#### 11.2.1 登录拦截逻辑
```php
public function checkLoginStatus($memberId)
{
    if (BaseMember::isCanceling($memberId)) {
        return [
            'canLogin' => false,
            'message' => '账号注销中，可撤回注销申请',
            'canWithdraw' => true
        ];
    }

    if (BaseMember::isCanceled($memberId)) {
        return [
            'canLogin' => false,
            'message' => '账号已注销',
            'canWithdraw' => false
        ];
    }

    return ['canLogin' => true];
}
```

#### 11.2.2 业务逻辑中的状态检查
```php
// 在需要检查用户状态的地方
if (!BaseMember::canUseNormally($memberId)) {
    throw new Exception('用户状态异常，无法执行此操作');
}
```
