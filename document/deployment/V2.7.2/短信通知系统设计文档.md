# V2.7.2 求职者注销功能短信通知系统设计文档

**版本**: V2.7.2  
**创建时间**: 2024-01-17  
**文档状态**: 已完成

## 1. 功能概述

基于V2.7.2求职者注销功能，设计了完整的短信通知系统，实现四个不同阶段的短信发送功能，确保用户在注销流程的每个关键节点都能收到及时、准确的通知。

## 2. 短信类型设计

### 2.1 短信类型常量定义

在 `common/libs/SmsQueue.php` 中定义了四种注销相关的短信类型：

```php
const TYPE_RESUME_CANCEL_VERIFY         = 18;  // 求职者注销身份验证码（需要验证）
const TYPE_RESUME_CANCEL_APPLY_SUCCESS  = 19;  // 求职者注销申请成功通知（纯通知）
const TYPE_RESUME_CANCEL_REMINDER       = 20;  // 求职者注销提醒（纯通知）
const TYPE_RESUME_CANCEL_COMPLETE       = 21;  // 求职者注销完成通知（纯通知）
```

### 2.2 短信类型分类

#### 2.2.1 验证码类短信
- **TYPE_RESUME_CANCEL_VERIFY**: 需要走验证业务逻辑
- **特点**:
  - 包含验证码，需要缓存用于后续验证
  - 有60秒发送时间限制
  - 验证码有效期5分钟
  - 支持 `validation()` 方法验证

#### 2.2.2 通知类短信
- **TYPE_RESUME_CANCEL_APPLY_SUCCESS**: 申请成功通知
- **TYPE_RESUME_CANCEL_REMINDER**: 注销提醒通知
- **TYPE_RESUME_CANCEL_COMPLETE**: 注销完成通知
- **特点**:
  - 纯通知功能，不包含验证码
  - 无发送时间限制
  - 不需要缓存
  - 不支持验证功能

### 2.2 短信内容模板

#### 2.2.1 身份验证短信 (TYPE_RESUME_CANCEL_VERIFY)
- **模板ID**: 1316951
- **内容**: `【求职者】验证码：{验证码}，5分钟内有效，用于账号注销身份验证，如非本人操作，请立即修改密码。`
- **发送时机**: 用户申请注销时，验证身份使用
- **特点**: 包含验证码，有效期5分钟，强调安全性

#### 2.2.2 申请成功通知短信 (TYPE_RESUME_CANCEL_APPLY_SUCCESS)
- **模板ID**: 1316952
- **内容**: `【求职者】您的注销申请已提交成功，将进入7天冷静期。期间您可以撤回申请，如有疑问请联系客服。`
- **发送时机**: 用户提交注销申请成功后立即发送
- **特点**: 告知冷静期机制，提醒可撤回

#### 2.2.3 注销提醒短信 (TYPE_RESUME_CANCEL_REMINDER)
- **模板ID**: 1316952
- **内容**: `【求职者】您的账号将于明天完成注销，如需撤回请及时登录操作。注销后数据无法恢复，请谨慎考虑。`
- **发送时机**: 冷静期结束前一天（第6天）发送
- **特点**: 最后提醒机会，强调数据不可恢复

#### 2.2.4 注销完成通知短信 (TYPE_RESUME_CANCEL_COMPLETE)
- **模板ID**: 1316952
- **内容**: `【求职者】您的账号注销已完成。感谢您曾经使用我们的服务，祝您工作顺利！如有疑问请联系客服。`
- **发送时机**: 注销流程最终完成后发送
- **特点**: 确认注销完成，表达感谢

## 3. 验证码与通知类短信的技术区别

### 3.1 SmsQueue.php 配置差异

#### 3.1.1 缓存键配置 (CACHE_KEY_LIST)
```php
const CACHE_KEY_LIST = [
    // ... 其他验证码类短信
    self::TYPE_RESUME_CANCEL_VERIFY => Cache::SMS_RESUME_CANCEL_VERIFY_KEY,
    // 注销通知类短信不在此列表中，不需要缓存
];
```

#### 3.1.2 时间限制配置 (LIMIT_TIME_TYPE)
```php
const LIMIT_TIME_TYPE = [
    // ... 其他验证码类短信
    self::TYPE_RESUME_CANCEL_VERIFY => 60,  // 60秒限制
    // 通知类短信不在此列表中
];
```

#### 3.1.3 无时间限制配置 (NO_LIMIT_TIME_TYPE)
```php
const NO_LIMIT_TIME_TYPE = [
    // ... 其他通知类短信
    self::TYPE_RESUME_CANCEL_APPLY_SUCCESS,  // 申请成功通知
    self::TYPE_RESUME_CANCEL_REMINDER,       // 注销提醒通知
    self::TYPE_RESUME_CANCEL_COMPLETE,       // 注销完成通知
];
```

### 3.2 核心方法行为差异

#### 3.2.1 setKey() 方法
- **验证码类**: 返回有效的缓存键
- **通知类**: 返回 null，不设置缓存键

#### 3.2.2 send() 方法
- **验证码类**: 生成验证码并缓存5分钟
- **通知类**: 不生成验证码，不使用缓存

#### 3.2.3 validation() 方法
- **验证码类**: 支持验证码验证
- **通知类**: 抛出异常 "该短信类型不支持验证码验证"

### 3.3 业务逻辑差异

#### 3.3.1 发送频率控制
- **验证码类**: 60秒内只能发送一次，防止恶意刷取
- **通知类**: 无发送频率限制，但通过业务状态控制避免重复发送

#### 3.3.2 错误处理策略
- **验证码类**: 发送失败会阻止业务流程继续
- **通知类**: 发送失败只记录日志，不影响主业务流程

## 4. 短信发送状态管理

### 3.1 状态常量定义

在 `BaseResumeCancelLog` 类中定义了短信发送状态：

```php
const SMS_STATUS_WAIT           = 0;    // 未发送短信
const SMS_STATUS_APPLY_SENT     = 1;    // 已发送申请成功短信
const SMS_STATUS_REMINDER_SENT  = 2;    // 已发送提醒短信（第6天）
const SMS_STATUS_COMPLETE_SENT  = 3;    // 已发送完成注销短信
```

### 3.2 状态转换流程

```
初始状态 (0) → 申请成功 (1) → 提醒发送 (2) → 完成发送 (3)
                ↓
            撤回申请时状态保持不变
```

## 4. 短信发送时机与业务流程集成

### 4.1 身份验证短信
- **触发位置**: `ResumeCancelService::sendCancelSms()`
- **发送条件**: 通过腾讯云滑块验证后
- **错误处理**: 验证码发送失败时抛出异常，阻止后续流程

### 4.2 申请成功通知短信
- **触发位置**: `ResumeCancelService::applyCancel()`
- **发送条件**: 注销申请创建成功，预处理操作完成后
- **错误处理**: 发送失败不影响主流程，记录错误日志
- **状态更新**: 发送成功后更新 `sms_status` 为 `SMS_STATUS_APPLY_SENT`

### 4.3 注销提醒短信
- **触发位置**: `ResumeCancelTimerService::sendCancelReminders()`
- **发送条件**: 定时任务每天执行，查找明天即将完成注销的用户
- **错误处理**: 发送失败记录错误，继续处理其他用户
- **状态更新**: 发送成功后更新 `sms_status` 为 `SMS_STATUS_REMINDER_SENT`

### 4.4 注销完成通知短信
- **触发位置**: `ResumeCancelDataCleanService::executeCancel()`
- **发送条件**: 系统自动执行注销时（人工操作不发送）
- **错误处理**: 发送失败不影响注销流程，记录错误日志
- **状态更新**: 发送成功后更新 `sms_status` 为 `SMS_STATUS_COMPLETE_SENT`

## 5. 定时任务设计

### 5.1 ResumeCancelTimerService 服务类

#### 5.1.1 发送注销提醒 (sendCancelReminders)
- **执行频率**: 每天执行一次
- **查询条件**: 明天冷静期结束的申请中状态记录
- **处理逻辑**: 
  1. 验证用户状态仍为注销中
  2. 发送提醒短信
  3. 更新短信发送状态
  4. 记录执行结果

#### 5.1.2 执行自动注销 (executeAutoCancel)
- **执行频率**: 每天执行一次
- **查询条件**: 今天冷静期结束的申请中状态记录
- **处理逻辑**:
  1. 验证冷静期确实已结束
  2. 调用注销执行服务
  3. 记录执行结果

#### 5.1.3 清理过期限制 (cleanExpiredRestrictions)
- **执行频率**: 每天执行一次
- **处理逻辑**: 清理180天后过期的注销限制记录

## 6. 错误处理与重试机制

### 6.1 错误处理策略

#### 6.1.1 验证码短信 (TYPE_RESUME_CANCEL_VERIFY)
- **失败处理**: 抛出异常，阻止用户继续申请流程
- **用户提示**: 显示"验证码发送失败，请稍后重试"
- **验证逻辑**: 支持 `validation()` 方法进行验证码验证
- **缓存机制**: 验证码缓存5分钟，验证后自动销毁

#### 6.1.2 通知类短信 (其他三种类型)
- **失败处理**: 记录错误日志，不影响主业务流程
- **监控告警**: 通过企业微信机器人发送告警信息
- **验证限制**: 不支持验证码验证，调用 `validation()` 方法会抛出异常
- **缓存策略**: 不使用缓存机制

### 6.2 重试机制

#### 6.2.1 队列重试
- **依赖**: 使用现有的 `Producer::sms()` 队列系统
- **重试次数**: 由队列系统配置决定
- **重试间隔**: 指数退避策略

#### 6.2.2 定时任务重试
- **提醒短信**: 下次定时任务执行时会重新尝试未发送的记录
- **完成通知**: 不重试，避免重复通知

## 7. 监控与日志

### 7.1 日志记录

#### 7.1.1 成功日志
```php
Yii::info("注销提醒短信发送成功，memberId: {$memberId}, mobile: {$mobile}", 'resume-cancel-timer');
```

#### 7.1.2 错误日志
```php
Yii::error("发送注销提醒失败：" . $e->getMessage(), 'resume-cancel-timer');
```

### 7.2 监控指标

- **短信发送成功率**: 按类型统计发送成功率
- **定时任务执行状态**: 记录每次执行的成功/失败数量
- **用户撤回率**: 统计收到提醒后的撤回比例

## 8. 安全考虑

### 8.1 防刷机制
- **验证码短信 (TYPE_RESUME_CANCEL_VERIFY)**: 60秒内同一手机号只能发送一次
- **通知短信 (其他三种类型)**: 无时间限制，但有业务逻辑控制，通过短信状态避免重复发送

### 8.2 内容安全
- **敏感信息**: 短信内容不包含用户敏感信息
- **防误导**: 明确告知用户操作的后果和撤回方式

## 9. 部署配置

### 9.1 短信模板配置
- **模板申请**: 需要在腾讯云短信服务中申请相应模板
- **模板审核**: 确保模板内容符合运营商要求

### 9.2 定时任务配置
```bash
# 每天上午9点发送注销提醒
0 9 * * * /path/to/yii resume-cancel/send-reminders

# 每天上午10点执行自动注销
0 10 * * * /path/to/yii resume-cancel/auto-cancel

# 每天凌晨2点清理过期限制
0 2 * * * /path/to/yii resume-cancel/clean-restrictions
```

## 10. 测试验证

### 10.1 功能测试
- **短信发送**: 验证各类型短信能正常发送
- **状态更新**: 验证短信状态正确更新
- **定时任务**: 验证定时任务正确执行

### 10.2 异常测试
- **网络异常**: 模拟短信服务不可用
- **数据异常**: 模拟用户数据不完整
- **并发测试**: 验证高并发下的稳定性

这套短信通知系统设计完整、安全可靠，与现有的短信队列系统无缝集成，为用户提供了完善的注销流程体验。
