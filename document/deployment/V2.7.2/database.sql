-- =====================================================
-- 求职者注销账号功能 - 数据库创建脚本
-- 创建时间: 2024-01-17
-- 说明: 包含求职者注销功能所需的所有数据表
-- =====================================================

-- ----------------------------
-- Table structure for resume_cancel_log
-- ----------------------------
DROP TABLE IF EXISTS `resume_cancel_log`;
CREATE TABLE `resume_cancel_log`
(
    `id`                   int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`             datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`          datetime      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `member_id`            int(11)       NOT NULL DEFAULT '0' COMMENT '求职者会员id',
    `resume_id`            int(11)       NOT NULL DEFAULT '0' COMMENT '简历id',
    `admin_id`             int(11)       NOT NULL DEFAULT '0' COMMENT '操作的运营id，在运营操作的时候会有这个',
    `cancel_reason_type`   tinyint(1)    NOT NULL DEFAULT '1' COMMENT '注销原因类型：1已找到工作，2不满意服务，3注册多个账号等等，99其他',
    `cancel_reason_detail` varchar(1024) NOT NULL DEFAULT '' COMMENT '注销原因详细说明（选择其他时填写）',
    `apply_time`           datetime      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '申请时间',
    `cooldown_end_time`    datetime      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '冷静期结束时间（申请时间+7天）',
    `complete_time`        datetime      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '注销完成时间',
    `withdraw_time`        datetime      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '撤回时间',
    `status`               tinyint(1)    NOT NULL DEFAULT '1' COMMENT '状态：1申请中，2已撤回，3已完成',
    `ip`                   varchar(45)   NOT NULL DEFAULT '' COMMENT 'IP地址（支持IPv4和IPv6）',
    `sms_status`           tinyint(1)    NOT NULL DEFAULT '1' COMMENT '短信发送状态：1已发送申请成功，2已发送提醒，3已发送完成注销',
    `resume_setting`       varchar(500)    NOT NULL DEFAULT '' COMMENT '求职者注销前消息通知json',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_apply_time` (`apply_time`) USING BTREE,
    KEY `idx_cooldown_end_time` (`cooldown_end_time`) USING BTREE,
    KEY `idx_status` (`status`) USING BTREE,
    KEY `idx_sms_status` (`sms_status`) USING BTREE,
    KEY `idx_member_status` (`member_id`, `status`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='求职者注销日志表';

-- ----------------------------
-- Table structure for resume_cancel_snapshot
-- ----------------------------
DROP TABLE IF EXISTS `resume_cancel_snapshot`;
CREATE TABLE `resume_cancel_snapshot`
(
    `id`               int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `member_id`        int(11)      NOT NULL DEFAULT '0' COMMENT '求职者会员id',
    `resume_id`        int(11)      NOT NULL DEFAULT '0' COMMENT '简历id',
    `cancel_log_id`    int(11)      NOT NULL DEFAULT '0' COMMENT '注销日志id',
    `mobile`           varchar(16)  NOT NULL DEFAULT '' COMMENT '手机号快照',
    `mobile_code`      varchar(16)  NOT NULL DEFAULT '' COMMENT '手机号区号快照',
    `email`            varchar(256) NOT NULL DEFAULT '' COMMENT '邮箱快照',
    `username`         varchar(32)  NOT NULL DEFAULT '' COMMENT '用户名快照',
    `name`             varchar(32)  NOT NULL DEFAULT '' COMMENT '姓名快照',
    `resume_data_json` text COMMENT '简历完整数据快照（JSON）',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_cancel_log_id` (`cancel_log_id`) USING BTREE,
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_mobile` (`mobile`) USING BTREE,
    KEY `idx_email` (`email`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='求职者数据快照表';

-- ----------------------------
-- Table structure for resume_cancel_restriction
-- ----------------------------
DROP TABLE IF EXISTS `resume_cancel_restriction`;
CREATE TABLE `resume_cancel_restriction`
(
    `id`                   int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`             datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`          datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `mobile`               varchar(16)  NOT NULL DEFAULT '' COMMENT '手机号',
    `mobile_code`          varchar(16)  NOT NULL DEFAULT '86' COMMENT '手机号区号',
    `email`                varchar(256) NOT NULL DEFAULT '' COMMENT '邮箱',
    `cancel_log_id`        int(11)      NOT NULL DEFAULT '0' COMMENT '注销日志id',
    `restriction_end_time` datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '限制结束时间（注销时间+180天）',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_mobile` (`mobile`, `mobile_code`) USING BTREE,
    KEY `idx_email` (`email`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='求职者注销限制表';


-- 为member表添加cancel_status字段
ALTER TABLE `member`
    ADD COLUMN `cancel_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '注销状态：0未申请注销，9注销中（冷静期），1注销成功（已完成）'
        AFTER `company_member_type`,
    MODIFY COLUMN `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态0删除,-1禁止登陆,1正常,9等待审核,-2非合作帐号,10账号已注销' AFTER `update_time`;

-- 为cancel_status字段添加索引，便于查询
ALTER TABLE `member`
    ADD INDEX `idx_cancel_status` (`cancel_status`) USING BTREE;
