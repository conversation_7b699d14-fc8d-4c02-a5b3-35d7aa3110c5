# V2.7.2 求职者注销功能短信发送对接总结

**版本**: V2.7.2  
**创建时间**: 2024-01-17  
**文档状态**: 已完成

## 1. 短信发送调用统一格式

### 1.1 Producer::sms() 方法参数说明
```php
Producer::sms($mobile, $userType, $smsType, $mobileCode, $extParams = '');
```

**参数说明**：
- `$mobile`: 手机号码
- `$userType`: 用户类型（使用BaseMember常量）
- `$smsType`: 短信类型（使用SmsQueue常量）
- `$mobileCode`: 手机区号（默认'86'）
- `$extParams`: 扩展参数（可选）

## 2. 注销功能中的短信发送对接

### 2.1 身份验证短信 (TYPE_RESUME_CANCEL_VERIFY)

#### 发送位置
**文件**: `common/service/memberCancel/ResumeCancelService.php`  
**方法**: `sendCancelSms()`  
**行号**: 第87行

#### 调用代码
```php
// 发送注销身份验证码
Producer::sms($mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_VERIFY, $mobileCode);
```

#### 参数对接
- `$mobile`: 用户手机号
- `$userType`: `BaseMember::TYPE_PERSON` (求职者类型)
- `$smsType`: `SmsQueue::TYPE_RESUME_CANCEL_VERIFY` (注销验证码类型)
- `$mobileCode`: 手机区号

#### 验证调用
**文件**: `common/service/memberCancel/ResumeCancelService.php`  
**方法**: `validateSmsCode()`  
**行号**: 第105行

```php
// 验证注销身份验证码
$sms = new SmsQueue($mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_VERIFY, $mobileCode);
if (!$sms->validation($code)) {
    throw new \Exception('验证码错误或已过期');
}
```

### 2.2 申请成功通知短信 (TYPE_RESUME_CANCEL_APPLY_SUCCESS)

#### 发送位置
**文件**: `common/service/memberCancel/ResumeCancelService.php`  
**方法**: `sendApplyNotification()`  
**行号**: 第298行

#### 调用代码
```php
// 发送注销申请成功通知
Producer::sms($mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_APPLY_SUCCESS, $mobileCode);
```

#### 参数对接
- `$mobile`: 用户手机号
- `$userType`: `BaseMember::TYPE_PERSON` (求职者类型)
- `$smsType`: `SmsQueue::TYPE_RESUME_CANCEL_APPLY_SUCCESS` (申请成功通知类型)
- `$mobileCode`: 手机区号

#### 触发时机
在 `applyCancel()` 方法中，注销申请创建成功后调用（第175行）

### 2.3 注销提醒短信 (TYPE_RESUME_CANCEL_REMINDER)

#### 发送位置
**文件**: `common/service/memberCancel/ResumeCancelTimerService.php`  
**方法**: `sendCancelReminders()`  
**行号**: 第67-68行

#### 调用代码
```php
// 发送注销提醒短信（冷静期第6天）
Producer::sms($member->mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_REMINDER, $member->mobile_code);
```

#### 参数对接
- `$mobile`: `$member->mobile` (用户手机号)
- `$userType`: `BaseMember::TYPE_PERSON` (求职者类型)
- `$smsType`: `SmsQueue::TYPE_RESUME_CANCEL_REMINDER` (注销提醒类型)
- `$mobileCode`: `$member->mobile_code` (用户手机区号)

#### 触发时机
定时任务每天执行，查找明天即将完成注销的用户

#### 备用调用方法
**文件**: `common/service/memberCancel/ResumeCancelService.php`  
**方法**: `sendCancelReminder()` (静态方法)  
**行号**: 第317行

```php
// 静态方法调用示例
ResumeCancelService::sendCancelReminder($mobile, $mobileCode);
```

### 2.4 注销完成通知短信 (TYPE_RESUME_CANCEL_COMPLETE)

#### 发送位置
**文件**: `common/service/memberCancel/ResumeCancelDataCleanService.php`  
**方法**: `sendCancelSuccessNotification()`  
**行号**: 第251-252行

#### 调用代码
```php
// 发送注销完成通知
Producer::sms($member->mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_COMPLETE, $member->mobile_code);
```

#### 参数对接
- `$mobile`: `$member->mobile` (用户手机号)
- `$userType`: `BaseMember::TYPE_PERSON` (求职者类型)
- `$smsType`: `SmsQueue::TYPE_RESUME_CANCEL_COMPLETE` (注销完成通知类型)
- `$mobileCode`: `$member->mobile_code` (用户手机区号)

#### 触发时机
在 `executeCancel()` 方法中，系统自动执行注销时发送（人工操作不发送）

## 3. 短信类型与常量对应关系

### 3.1 SmsQueue 常量定义
```php
const TYPE_RESUME_CANCEL_VERIFY         = 18;  // 求职者注销身份验证码
const TYPE_RESUME_CANCEL_APPLY_SUCCESS  = 19;  // 求职者注销申请成功通知
const TYPE_RESUME_CANCEL_REMINDER       = 20;  // 求职者注销提醒（冷静期第6天）
const TYPE_RESUME_CANCEL_COMPLETE       = 21;  // 求职者注销完成通知
```

### 3.2 BaseMember 用户类型常量
```php
const TYPE_PERSON  = 1;  // 求职者（个人用户）
const TYPE_COMPANY = 2;  // 企业用户
```

## 4. 错误处理策略

### 4.1 验证码短信错误处理
```php
// 发送失败会抛出异常，阻止业务流程
try {
    Producer::sms($mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_VERIFY, $mobileCode);
} catch (\Exception $e) {
    throw new \Exception('验证码发送失败，请稍后重试');
}
```

### 4.2 通知类短信错误处理
```php
// 发送失败只记录日志，不影响主业务流程
try {
    Producer::sms($mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_APPLY_SUCCESS, $mobileCode);
} catch (\Exception $e) {
    Yii::error('发送注销申请通知失败：' . $e->getMessage(), 'resume-cancel');
}
```

## 5. 调用示例总结

### 5.1 完整的注销流程短信发送
```php
// 1. 发送验证码（用户申请时）
Producer::sms($mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_VERIFY, $mobileCode);

// 2. 验证验证码
$sms = new SmsQueue($mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_VERIFY, $mobileCode);
$sms->validation($code);

// 3. 发送申请成功通知（申请提交后）
Producer::sms($mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_APPLY_SUCCESS, $mobileCode);

// 4. 发送注销提醒（第6天，定时任务）
Producer::sms($mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_REMINDER, $mobileCode);

// 5. 发送注销完成通知（第7天，系统自动）
Producer::sms($mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_COMPLETE, $mobileCode);
```

## 6. 注意事项

### 6.1 参数一致性
- 所有注销相关短信都使用 `BaseMember::TYPE_PERSON` 作为用户类型
- 短信类型必须使用 `SmsQueue::TYPE_RESUME_CANCEL_*` 常量
- 手机区号统一使用用户的 `mobile_code` 字段

### 6.2 验证码特殊性
- 只有 `TYPE_RESUME_CANCEL_VERIFY` 支持验证码验证
- 其他三种类型调用 `validation()` 方法会抛出异常

### 6.3 发送时机控制
- 验证码短信：用户主动触发
- 申请成功通知：申请提交后立即发送
- 注销提醒：定时任务在第6天发送
- 注销完成通知：系统自动注销时发送（人工操作不发送）

所有短信发送调用已正确对接到相应的类型和smsType，确保了系统的一致性和可维护性。
