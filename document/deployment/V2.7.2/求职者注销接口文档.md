# 求职者注销功能接口文档

**版本**: V2.7.2
**创建时间**: 2025-07-22
**适用平台**: PC端、H5端、小程序端
**基础路径**: `/api/person/resume/`

---

## 1. 获取注销配置信息

### 基本信息

- **接口名称**: 获取注销配置信息
- **请求方式**: GET
- **接口地址**: `/api/person/resume/get-cancel-config`
- **需要登录**: 是

### 请求参数

无

### 返回参数

```json
{
  "msg": "操作成功",
  "result": 1,
  "data": {
    "cancelReasonList": [
      {
        "value": 1,
        "label": "已找到新工作，以后不再打算找工作",
        "notice": "恭喜您找到新工作，建议您隐藏简历，不被打扰，再有需要时，仍可继续使用呢"
      },
      {
        "value": 2,
        "label": "换手机号了，重新注册",
        "notice": "无须注销账号，简单两步，换绑新手机号"
      },
      {
        "value": 3,
        "label": "注册企业账号，误操作为个人账号",
        "notice": "您可以直接隐藏简历，不展示给任何人，无须注销账号"
      },
      {
        "value": 4,
        "label": "已有多个账号，想注销一个",
        "notice": ""
      },
      {
        "value": 5,
        "label": "担心隐私泄露",
        "notice": ""
      },
      {
        "value": 6,
        "label": "不想接收到邀约邮件",
        "notice": "关闭职位订阅，将不再接收任何订阅类邀约邮件及通知，是否确认关闭？"
      },
      {
        "value": 99,
        "label": "其他原因",
        "notice": ""
      }
    ],
    "userMobileCode": "86",
    "userMobile": "138****8888"
  }
}
```

### 字段说明


| 字段名                    | 类型   | 说明                                     |
| ------------------------- | ------ | ---------------------------------------- |
| cancelReasonList          | Array  | 注销原因列表                             |
| cancelReasonList[].value  | Number | 注销原因类型值                           |
| cancelReasonList[].label  | String | 注销原因显示文本                         |
| cancelReasonList[].notice | String | 温馨提示文案（引导用户考虑其他解决方案） |
| userMobileCode            | String | 用户手机号区号                           |
| userMobile                | String | 用户手机号（脱敏显示）                   |

### Apifox导入参数格式

```
无参数
```

---

## 2. 检查注销资格

### 基本信息

- **接口名称**: 检查注销资格
- **请求方式**: POST
- **接口地址**: `/api/person/resume/check-cancel-eligibility`
- **需要登录**: 是

### 请求参数

无

### 返回参数

#### 无生效服务时

```json
{
  "msg": "操作成功",
  "result": 1,
  "data": {
    "hasActiveServices": false
  }
}
```

#### 有生效服务时

```json
{
  "msg": "操作成功",
  "result": 1,
  "data": {
    "hasActiveServices": true,
    "serviceWarning": "您当前有生效中的简历置顶、简历刷新。注销账号后，所有资源将被彻底清空且无法恢复，您确定要注销吗？"
  }
}
```

### 字段说明


| 字段名            | 类型    | 说明                                                                  |
| ----------------- | ------- | --------------------------------------------------------------------- |
| hasActiveServices | Boolean | 是否有生效中的求职服务                                                |
| serviceWarning    | String  | 服务警告提示（仅当hasActiveServices为true时返回，包含具体的服务名称） |

### Apifox导入参数格式

```
无参数
```

---

## 3. 发送注销短信验证码

### 基本信息

- **接口名称**: 发送注销短信验证码
- **请求方式**: POST
- **接口地址**: `/api/person/resume/send-cancel-sms`
- **需要登录**: 是

### 请求参数


| 参数名  | 类型   | 必需 | 示例值    | 说明                    |
| ------- | ------ | ---- | --------- | ----------------------- |
| ticket  | String | 是   | t03xxxxxx | 腾讯云滑块验证码ticket  |
| randstr | String | 是   | @xxxxxx   | 腾讯云滑块验证码randstr |

### 返回参数

```json
{
  "msg": "验证码发送成功",
  "result": 1,
  "data": []
}
```

### Apifox导入参数格式

```
ticket,String,是,t03xxxxxx,-,腾讯云滑块验证码ticket
randstr,String,是,@xxxxxx,-,腾讯云滑块验证码randstr
```

---

## 4. 提交注销申请

### 基本信息

- **接口名称**: 提交注销申请
- **请求方式**: POST
- **接口地址**: `/api/person/resume/apply-cancel`
- **需要登录**: 是

### 请求参数


| 参数名             | 类型   | 必需 | 示例值       | 说明                                                                                              |
| ------------------ | ------ | ---- | ------------ | ------------------------------------------------------------------------------------------------- |
| cancelReasonType   | Number | 是   | 1            | 注销原因类型：1=已找到工作，2=换手机号，3=误操作，4=多个账号，5=隐私担忧，6=不想接收邮件，99=其他 |
| cancelReasonDetail | String | 否   | 具体原因说明 | 注销原因详细说明（选择其他原因时必填）                                                            |
| smsCode            | String | 是   | 123456       | 短信验证码                                                                                        |

### 返回参数

```json
{
  "msg": "注销申请提交成功",
  "result": 1,
  "data": {
    "cancelLogId": 123,
    "cooldownEndTime": "2024-01-24 15:30:00"
  }
}
```

### 字段说明


| 字段名          | 类型   | 说明           |
| --------------- | ------ | -------------- |
| cancelLogId     | Number | 注销日志ID     |
| cooldownEndTime | String | 冷静期结束时间 |

### Apifox导入参数格式

```
cancelReasonType,Number,是,1,-,注销原因类型：1=已找到工作，2=换手机号，3=误操作，4=多个账号，5=隐私担忧，6=不想接收邮件，99=其他
cancelReasonDetail,String,否,具体原因说明,-,注销原因详细说明（选择其他原因时必填）
smsCode,String,是,123456,-,短信验证码
```

---

## 错误码说明

### 通用错误码


| 错误码 | 错误信息                             | 说明                 |
| ------ | ------------------------------------ | -------------------- |
| 0      | 用户不存在                           | 用户账号异常         |
| 0      | 简历不存在                           | 用户简历数据异常     |
| 0      | 用户状态异常，无法申请注销           | 用户账号状态不正常   |
| 0      | 您已有进行中的注销申请，请勿重复提交 | 存在未完成的注销申请 |

### 短信验证码相关错误


| 错误码 | 错误信息           | 说明               |
| ------ | ------------------ | ------------------ |
| 0      | 图形验证码验证失败 | 腾讯云滑块验证失败 |
| 0      | 手机号不能为空     | 手机号参数缺失     |
| 0      | 短信发送失败       | 短信服务异常       |
| 0      | 验证码错误或已过期 | 验证码验证失败     |

### 注销申请相关错误


| 错误码 | 错误信息                       | 说明                         |
| ------ | ------------------------------ | ---------------------------- |
| 0      | 注销原因类型不能为空           | 必填参数缺失                 |
| 0      | 选择其他原因时必须填写详细说明 | 选择其他原因时详细说明为必填 |

---

## 业务流程说明

### 注销申请流程

1. **获取配置信息** - 调用 `get-cancel-config` 获取注销原因列表、温馨提示和用户手机号信息
2. **检查注销资格** - 调用 `check-cancel-eligibility` 检查是否可以申请注销，获取生效服务信息
3. **图形验证** - 前端完成腾讯云滑块验证，获取ticket和randstr
4. **发送验证码** - 调用 `send-cancel-sms` 发送短信验证码（需要图形验证参数）
5. **提交申请** - 调用 `apply-cancel` 提交注销申请

### 注销后的变化

- 用户状态变更为"注销中"
- 立即执行预处理操作：
  - 解绑微信/小程序
  - 退出人才库
  - 关闭职位订阅
  - 关闭消息通知
- 进入7天冷静期
- 冷静期内可撤回注销申请

### 新增参数说明

在V2.7.2版本中，`get-cancel-config`接口新增了两个用户信息参数：

| 参数名 | 用途 | 说明 |
|--------|------|------|
| userMobileCode | 短信发送 | 用于发送验证码时的手机区号，前端无需用户再次输入 |
| userMobile | 界面显示 | 脱敏后的手机号，用于在注销页面显示"将向138****8888发送验证码" |

### 温馨提示功能说明

系统会根据用户选择的注销原因，提供相应的温馨提示和替代解决方案：


| 注销原因     | 温馨提示                                                               | 目的                       |
| ------------ | ---------------------------------------------------------------------- | -------------------------- |
| 已找到新工作 | 恭喜您找到新工作，建议您隐藏简历，不被打扰，再有需要时，仍可继续使用呢 | 引导用户使用隐藏简历功能   |
| 换手机号了   | 无须注销账号，简单两步，换绑新手机号                                   | 引导用户使用换绑手机号功能 |
| 误操作注册   | 您可以直接隐藏简历，不展示给任何人，无须注销账号                       | 引导用户使用隐藏简历功能   |
| 不想接收邮件 | 关闭职位订阅，将不再接收任何订阅类邮约邮件及通知，是否确认关闭？       | 引导用户关闭职位订阅功能   |

### 注意事项

1. 注销申请提交后立即生效，无法撤销预处理操作
2. 冷静期为7天，期间可以撤回注销申请
3. 冷静期结束后将执行最终注销，数据无法恢复
4. 注销完成后180天内无法使用相同手机号或邮箱重新注册
5. 发送短信验证码需要先完成腾讯云滑块验证
