# ReadMe

### 关联产品原型版本

- 账号注销（[蓝湖](https://lanhuapp.com/web/#/item/project/product?tid=8d951de4-aefb-40f7-954e-366683d68331&pid=3399fe1e-e6ca-4b11-b08a-e23b9e39f9f7&versionId=4d896ea5-f94c-4f90-a7cf-9b4f59c5968b&docId=8ae164a7-54a4-4fac-aad3-86b26b633c75&docType=axure&pageId=77cfc171cb064c20b6cd9fda2b29413e&image_id=8ae164a7-54a4-4fac-aad3-86b26b633c75)）

***

### 参与人员

- 龚传栋
- 陈钊发
- 杜孙鹤
- 伍彦川

***

### 分支

|仓库|开发分支|提測分支|备注|
|:----:|:----:|:----:|:----:|
|new_gaoxiao_yii|hotfix/V2.7.2_求职者注销||-|
|new_gaoxiao_admin_pc_vue|hotfix/V2.7.2||-|
|new_gaoxiao_admin_company_vue|hotfix/v2.7.2||-|
|new_gaoxiao_person_pc_vue|hotfix/v2.7.2|||
|||||

***

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤| 是否执行 |执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|  -   |路由名称：name=注销管理，key=resumeCance和它的子路由注销申请列表   resumeCancelList|
|执行sql语句|  -   |见下方"执行sql语句"|
|更新后端代码|  是   |-|
|composer安装|  -   |见下方"composer安装"|
|更新配置|  -   |见下方"更新配置"|
|创建队列|  -   |见下方"创建队列"|
|执行脚本|  是   |见下方"执行脚本"|
|删除redis缓存|  -   |见下方"删除redis缓存"|
|重启队列|  -   |上线前, 暂停所有队列, 上线后再重启|
|更新前端代码|  -   |-|
|添加定时任务|  -   |见下方"定时任务"|
|群内通知部署完毕|  -   |-|



#### 执行脚本

* 每天中午12点执行  php timer_yii resume-cancel/send-notice-sms  
* 每天晚上11点50分执行  php timer_yii resume-cancel/submit-apply
* 每天早上8点30分执行  php timer_yii resume-cancel/send-complete-sms

#### 路由添加

```
路由名称：name=注销管理，key=resumeCance和它的子路由注销申请列表   resumeCancelList

```