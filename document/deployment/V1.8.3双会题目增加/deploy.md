# ReadMe

### 关联产品原型版本

- [蓝湖 (lanhuapp.com)](https://lanhuapp.com/web/#/item/project/product?pid=baabeb62-ec06-46e5-b503-4085c32254f3&versionId=249c31a2-9679-4ee8-98da-0feb3adaa004&docId=3c48e763-68ae-4af0-b9b1-33b5bda0b159&docType=axure&pageId=aa9f644d6d3d4806869796f414a656b5&image_id=3c48e763-68ae-4af0-b9b1-33b5bda0b159&tid=8d951de4-aefb-40f7-954e-366683d68331)

***

### 参与人员

- 龚传栋
- 杜孙鹤



***

### [alter_data.sql](sql_backup/alter_data.sql) 分支

|仓库|开发分支|提測分支|备注|
|:----:|:----:|:----:|:----:|
|new_gaoxiao_yii|feature/V1.8.3双会增加题库|release/V1.8.3双会增加题库|-|
|new_gaoxiao_admin_pc_vue|feature/V1.8.3双会增加题库|release/V1.8.3双会增加题库|-|

***

### 前端路由

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤|是否执行|执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|-|是|
|执行sql语句|是|见下方"执行sql语句"|
|更新后端代码|是|-|
|composer安装|是|见下方"composer安装"|
|更新配置|-|见下方"更新配置"|
|创建队列|-|见下方"创建队列"|
|执行脚本|是|见下方"执行脚本"|
|删除redis缓存|-|见下方"删除redis缓存"|
|重启队列|-|上线前, 暂停所有队列, 上线后再重启|
|更新前端代码|-|是|
|添加定时任务|是|见下方"定时任务"|
|群内通知部署完毕|是|-|

#### 执行sql语句(按顺序执行)

- alter_data.sql

#### 执行脚本

- php timer_yii script/update-activity-form-option-sign
- php timer_yii script/update-activity-form-intention-option-sign