<?php

namespace timer\controllers;

use common\base\models\BaseMember;
use common\base\models\BaseResumeCancelLog;
use common\helpers\TimeHelper;
use common\libs\SmsQueue;
use common\service\memberCancel\ResumeCancelDataCleanService;
use queue\Producer;

/**
 *
 */
class ResumeCancelController extends BaseTimerController
{

    // php timer_yii resume-cancel/send-notice-sms
    public function actionSendNoticeSms()
    {
        // 发送提醒短信     • 发起注销后的第6天（含发起注销日）12:00推送短信通知：
        // 找到所有申请中，并且发送短信状态是1阶段的数据出来，进行发送，然后更改成2阶段
        $list = BaseResumeCancelLog::find()
            ->where([
                'status'     => BaseResumeCancelLog::STATUS_APPLYING,
                'sms_status' => BaseResumeCancelLog::SMS_STATUS_APPLY_SENT,
            ])
            ->all();

        self::log('一共有' . count($list) . '条数据');

        $todayDay = date('Y-m-d');

        foreach ($list as $item) {
            // 校验一下用户是不是真正的申请注销
            $member = BaseMember::findOne($item['member_id']);

            if (!$member) {
                self::log('用户不存在' . $item->id);
                continue;
            }

            if ($member->cancel_status != BaseMember::STATUS_CANCEL_STATUS_CANCELING) {
                self::log('用户状态不是申请注销' . $item->id);
                continue;
            }

            // 看时间，如果今天是第六天，就发送提醒
            $applyTime = $item['apply_time'];
            $applyDate = date('Y-m-d', strtotime($applyTime));

            $subDay = TimeHelper::reduceDates($todayDay, $applyDate);

            self::log('申请时间是' . $applyTime . '，申请日期是' . $applyDate . '，今天是' . $todayDay . '，相差' . $subDay . '天');

            if ($subDay == 5) {
                // 发送短信

                Producer::sms($member->mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_REMINDER,
                    $member->mobile_code);

                // 入队成功
                $item->sms_status = BaseResumeCancelLog::SMS_STATUS_REMINDER_SENT;
                $item->save();

                self::log('发送成功' . $item->id);
            }
        }
    }

    // php timer_yii resume-cancel/submit-apply
    public function actionSubmitApply()
    {
        $list = BaseResumeCancelLog::find()
            ->where([
                'status' => BaseResumeCancelLog::STATUS_APPLYING,
            ])
            ->all();

        self::log('一共有' . count($list) . '条数据');

        $todayDay = date('Y-m-d');
        foreach ($list as $item) {
            $member = BaseMember::findOne($item['member_id']);

            if (!$member) {
                self::log('用户不存在' . $item->id);
                continue;
            }

            if ($member->cancel_status != BaseMember::STATUS_CANCEL_STATUS_CANCELING) {
                self::log('用户状态不是申请注销' . $item->id);
                continue;
            }

            // 看时间，如果今天是第7天，就执行注销
            $applyTime = $item['apply_time'];
            $applyDate = date('Y-m-d', strtotime($applyTime));

            $subDay = TimeHelper::reduceDates($todayDay, $applyDate);

            self::log('申请时间是' . $applyTime . '，申请日期是' . $applyDate . '，今天是' . $todayDay . '，相差' . $subDay . '天');

            if ($subDay >= 6) {
                // 开启事务
                $transaction = \Yii::$app->db->beginTransaction();
                try {
                    $service = new ResumeCancelDataCleanService();
                    $service->executeCancel($item->id, 0); // 传递注销日志ID和管理员ID(0表示系统操作)

                    $transaction->commit();

                    self::log('执行成功' . $item->id);
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    self::log('执行失败' . $e->getMessage());
                }
            }
        }
    }

    // php timer_yii resume-cancel/send-complete-sms
    public function actionSendCompleteSms()
    {
        // 发送注销完成短信 - 次日8:30发送
        // 找到所有已完成注销，但还没有发送完成短信的记录
        $list = BaseResumeCancelLog::find()
            ->where([
                'status'     => BaseResumeCancelLog::STATUS_COMPLETED,
                'sms_status' => BaseResumeCancelLog::SMS_STATUS_REMINDER_SENT,
                // 还没发送完成短信
            ])
            ->all();

        self::log('一共有' . count($list) . '条需要发送注销完成短信的数据');

        $todayDay = date('Y-m-d');

        foreach ($list as $item) {
            // 校验一下用户状态
            $member = BaseMember::findOne($item['member_id']);

            if (!$member) {
                self::log('用户不存在' . $item->id);
                continue;
            }

            if ($member->cancel_status != BaseMember::CANCEL_STATUS_CANCELED) {
                self::log('用户状态不是已注销' . $item->id);
                continue;
            }

            // 检查注销完成时间，如果是昨天完成的注销，今天8:30发送短信
            $completeTime = $item['complete_time'];
            $completeDate = date('Y-m-d', strtotime($completeTime));

            // 计算完成注销日期与今天的差值
            $subDay = TimeHelper::reduceDates($todayDay, $completeDate);

            self::log('注销完成时间是' . $completeTime . '，完成日期是' . $completeDate . '，今天是' . $todayDay . '，相差' . $subDay . '天');

            // 发送注销完成短信
            Producer::sms($member->mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_CANCEL_COMPLETE,
                $member->mobile_code);

            // 更新短信发送状态
            $item->sms_status = BaseResumeCancelLog::SMS_STATUS_COMPLETE_SENT;
            $item->save();

            self::log('发送注销完成短信成功' . $item->id);
        }
    }
}
