<?php

namespace common\libs;

use common\helpers\DebugHelper;
use EasyWeChat\Factory;
use EasyWeChat\Kernel\Http\StreamResponse;
use Yii;
use function EasyWeChat\Kernel\Support\str_random;

/**
 * 微信小程序相关
 */
class WxMiniApp
{
    // 初始化
    private static $instance = null;
    private static $app;

    private $config;

    private $type;

    const TYPE_RESUME  = 1;
    const TYPE_COMPANY = 2;

    const QRCODE_PATH_TYPE_JOB              = 'JOB';
    const QRCODE_PATH_TYPE_ANNOUNCEMENT     = 'ANNOUNCEMENT';
    const QRCODE_PATH_TYPE_COMPANY          = 'COMPANY';
    const QRCODE_PATH_TYPE_LOGIN            = 'LOGIN';
    const QRCODE_PATH_TYPE_ACTIVITY         = 'ACTIVITY';
    const QRCODE_PATH_TYPE_SPECIAL_ACTIVITY = 'SPECIAL_ACTIVITY';

    const QRCODE_PATH_TYPE_TO_ROUTE = [
        self::QRCODE_PATH_TYPE_JOB              => 'packages/job/detail/index',
        self::QRCODE_PATH_TYPE_ANNOUNCEMENT     => 'packages/announcement/detail/index',
        self::QRCODE_PATH_TYPE_COMPANY          => 'packages/company/detail/index',
        self::QRCODE_PATH_TYPE_LOGIN            => 'packages/auth/index',
        self::QRCODE_PATH_TYPE_ACTIVITY         => 'packages/discover/activity/index',
        self::QRCODE_PATH_TYPE_SPECIAL_ACTIVITY => 'packages/discover/special/index',
    ];

    // 等等扫码
    const LOGIN_QRCODE_STATUS_WAIT = 1;
    // 已经扫码了
    const LOGIN_QRCODE_STATUS_SCAN = 2;
    // 登录了
    const LOGIN_QRCODE_STATUS_LOGIN = 3;
    //错误
    const LOGIN_QRCODE_STATUS_FAIL = -1;

    public function __construct($type)
    {
        switch ($type) {
            case self::TYPE_RESUME:
                $config = Yii::$app->params['wx']['personMiniApp'];
                break;
            case self::TYPE_COMPANY:
                $config = Yii::$app->params['wx']['companyMiniApp'];
                break;
            default:
                throw new \Exception('未知的微信小程序类型');
        }

        $this->type   = $type;
        $this->config = $config;

        self::$app = Factory::miniProgram($config);
    }

    public static function getInstance($type = self::TYPE_RESUME): WxMiniApp
    {
        if (empty(self::$instance)) {
            self::$instance = new self($type);
        }

        return self::$instance;
    }

    /**
     * 返回手机信息
     * @param $code
     * @return false|mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function codeToMobileInfo($code)
    {
        $app = self::$app;

        try {
            $data = $app->phone_number->getUserPhoneNumber($code);
        } catch (\Exception $e) {
            return false;
        }

        if ($data['errmsg'] == 'ok') {
            return $data['phone_info'];
        }

        return false;
    }

    public function generateUrl($url)
    {
        $app = self::$app;

        try {
            $data = $app->url_link->generate(['path' => $url]);
        } catch (\Exception $e) {
            return false;
        }

        if ($data['errmsg'] == 'ok') {
            return $data['url_link'];
        }

        return false;
    }

    public function codeToUserInfo($code)
    {
        $app = self::$app;
        try {
            $data = $app->auth->session($code);

            return $data;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 创建小程序登录二维码，64位返回
     * @return array
     */
    public function createLoginQrCode()
    {
        $app  = self::$app;
        $page = self::QRCODE_PATH_TYPE_TO_ROUTE[self::QRCODE_PATH_TYPE_LOGIN];

        $scene    = Yii::$app->security->generateRandomString();
        $cacheKey = Cache::PC_RESUME_MINI_QR_CODE_LOGIN_INFO_KEY;
        $key      = $cacheKey . ':' . $scene;
        $data     = json_encode([
            'status' => self::LOGIN_QRCODE_STATUS_WAIT,
            'time'   => time(),
        ]);

        // 考虑到服务器压力巨大，暂时设置为2分钟
        Cache::set($key, $data, 120);

        $response = $app->app_code->getUnlimit($scene, [
            'page'       => $page,
            'check_path' => false,
        ]);

        $contents = $response->getBody()
            ->getContents();

        // 如果非正式环境
        if (Yii::$app->params['environment'] != 'prod') {
            // 使用scene 生成一个base64的图片
            $url = QrCodeLib::getQrCodeBase64('/' . self::QRCODE_PATH_TYPE_TO_ROUTE[self::QRCODE_PATH_TYPE_LOGIN] . '?scene=' . $scene);
        } else {
            $url = 'data:image/jpeg;base64,' . base64_encode($contents);
        }

        return [
            'scene' => $scene,
            'url'   => $url,
        ];
    }

    /**
     * 更新登录二维码状态
     * @param $token
     * @param $status
     * @return void
     * @throws \Exception
     */
    public function updateLoginQrCodeStatus($scene, $newStatus, $memberId = '')
    {
        $cacheKey = Cache::PC_RESUME_MINI_QR_CODE_LOGIN_INFO_KEY;
        $key      = $cacheKey . ':' . $scene;
        $data     = Cache::get($key);
        if (!$data) {
            //特殊情况，直接报错
            $data['status'] = self::LOGIN_QRCODE_STATUS_FAIL;
        }
        //更新缓存数据
        $data           = json_decode($data, true);
        $data['status'] = $newStatus;
        if ($newStatus == self::LOGIN_QRCODE_STATUS_LOGIN && $memberId) {
            //登录成功了，保存用户id给轮询那边使用
            $data['memberId'] = $memberId;
        }

        Cache::set($key, json_encode($data));
    }

    /**
     * 检查小程序登录二维码状态
     * @param $scene
     * @return bool|void
     * @throws \Exception
     */
    public function checkLoginQrCodeStatus($scene)
    {
        $cacheKey = Cache::PC_RESUME_MINI_QR_CODE_LOGIN_INFO_KEY;
        $key      = $cacheKey . ':' . $scene;
        $data     = Cache::get($key);
        $data     = json_decode($data, true);

        $createTime = $data['time'];

        if ($createTime && time() - $createTime > 60) {
            // 60秒过期
            $data['status'] = self::LOGIN_QRCODE_STATUS_FAIL;
            Cache::set($key, json_encode($data));
        }

        if (!$data) {
            throw new \Exception('非法扫码');
        }
        if ($data['status'] == self::LOGIN_QRCODE_STATUS_LOGIN) {
            // token校验通过,销毁这个缓存
            Cache::delete($key);
        }

        if ($data['status'] == self::LOGIN_QRCODE_STATUS_FAIL) {
            $data['tips'] = '二维码过期';
        }

        return $data;
    }

    public function createQrCodeByType($type, $params)
    {
        switch ($type) {
            // 公告
            case self::QRCODE_PATH_TYPE_JOB:
            case self::QRCODE_PATH_TYPE_ANNOUNCEMENT:
            case self::QRCODE_PATH_TYPE_COMPANY:
            case self::QRCODE_PATH_TYPE_ACTIVITY:
            case self::QRCODE_PATH_TYPE_SPECIAL_ACTIVITY:
                $page = self::QRCODE_PATH_TYPE_TO_ROUTE[$type];
                $id   = $params;

                $app = self::$app;

                // 前端需要解码
                $scene = http_build_query(['id' => $id]);

                $response = $app->app_code->getUnlimit($scene, [
                    'page'        => $page,
                    'is_hyaline'  => true,
                    'env_version' => Yii::$app->params['environment'] == 'prod' ? 'release' : 'develop',
                    //体验版trial，正式版release,开发版为 develop，rediskey最好区分一下环境
                ]);

                break;
            default:
                throw new \Exception('未知的二维码类型');
        }

        // 保存小程序码到文件
        if ($response instanceof \EasyWeChat\Kernel\Http\StreamResponse) {
            $ymd      = date("Ymd");
            $savePath = Yii::getAlias('@base') . '/uploads/tmp/miniapp/code/' . $ymd;
            $pathName = $type . '_' . $id . '.jpg';
            $filename = $response->save($savePath, $pathName);

            // 上传到七牛
            $qiniuOss  = new Qiniu('file');
            $qiniuPath = 'uploads/mini_code/' . $filename;

            $fileFullPath = $savePath . '/' . $filename;
            $qiniuOss->upload($fileFullPath, $qiniuPath);

            // 删除本地文件
            unlink($fileFullPath);

            return $qiniuOss->getShowUrl() . '/' . $qiniuPath;
        }
    }

    /**
     * 获取Scheme码，用于h5跳转小程序
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getSchemeUrl()
    {
        try {
            $schemeInfo = self::$app->url_scheme->generate();

            return $schemeInfo['openlink'];
        } catch (\Exception $e) {
            return '';
        }
    }

    public function getPlaintextSchemeUrl()
    {
        $config = Yii::$app->params['wxpay'];
        $url    = 'weixin://dl/business/?appid=*' . $config['appid'] . '*&path=*/packages/index/index*&query=*QUERY*&env_version=*ENV_VERSION*';

        return $url;
    }

    /**
     * 获取求职者小程序跳转链接
     * 加的page需要去小程序管理后台加链接，否则打不开
     */
    public static function getPersonSchemeUrl($page = '')
    {
        $pages = [
            'home'      => 'pages/home/<USER>',
            'chat'      => 'pages/chat/index',
            'activity'  => 'packages/discover/gather/index',
            'jobInvite' => 'pages/person/index',
        ];

        $url = 'weixin://dl/business/?appid=wx0a8ff20cfeff7f32&path=' . $pages[$page] ?? '';;

        if (Yii::$app->params['environment'] !== 'prod') {
            // 添加开发版环境变量
            $url .= '&env_version=develop';
        }

        return $url;
    }

}
