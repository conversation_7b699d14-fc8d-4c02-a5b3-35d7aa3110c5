<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseAdmin;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyCollect;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseJob;
use common\base\models\BaseJobEdit;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseJobLog;
use common\base\models\BaseMemberMessage;
use common\base\models\BaseUser;
use common\helpers\IpHelper;
use common\helpers\TimeHelper;
use common\libs\ColumnAuto\JobAutoClassify;
use common\service\companyPackage\CompanyPackageApplication;
use common\service\v2\announcement\AfterService;
use queue\Producer;
use Yii;
use yii\base\Exception;

/**
 * 职位审核状态流转
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class AuditStatusService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 纯职位审核
     * @return boolean
     * @throws Exception
     * @throws \yii\base\NotSupportedException
     */
    public function run()
    {
        $this->params = Yii::$app->request->post();
        if (!$this->params['id']) {
            throw new Exception('参数错误');
        }
        //获取职位信息
        $jobInfo = BaseJob::findOne($this->params['id']);
        if (!$jobInfo || $jobInfo->audit_status != BaseJob::AUDIT_STATUS_WAIT_AUDIT) {
            throw new Exception('职位不存在或职位不在审核阶段');
        }
        //判断职位审核提交审核状态
        if (!isset($this->params['auditStatus']) || !in_array($this->params['auditStatus'], [
                BaseJob::AUDIT_STATUS_PASS_AUDIT,
                BaseJob::AUDIT_STATUS_REFUSE_AUDIT,
            ])) {
            throw new Exception('职位审核操作状态不被允许！');
        }
        if ($this->params['opinion'] && mb_strlen($this->params['opinion']) > 50) {
            throw new Exception('审核拒绝时，审核处理意见不能超过50字！');
        }
        $this->jobId      = $this->params['id'];
        $this->oldJobInfo = BaseJob::findOne($this->params['id']);
        //将单位ID塞入参数
        $this->params['companyId'] = $jobInfo->company_id;
        //初始化账号信息
        $this->initInfo();
        $isFirstPublish = false;
        if ($this->params['auditStatus'] == BaseJob::AUDIT_STATUS_PASS_AUDIT) {
            //审核通过
            /// 分两种情况：
            /// 1、第一次审核通过的职位
            /// 2.非第一次审核通过的职位
            if ($jobInfo->status == BaseJob::STATUS_WAIT) {
                // 首次通过审核的职位----1
                $isFirstPublish              = true;
                $jobInfo->status             = BaseJob::STATUS_ONLINE;
                $jobInfo->first_release_time = CUR_DATETIME;
                $jobInfo->is_first_release   = $this->companyInfo->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES ? BaseJob::IS_FIRST_RELEASE_YES : BaseJob::IS_FIRST_RELEASE_NO;
                //写入初次审核通过日志
                $this->log(BaseJobLog::TYPE_AUDIT_AGREE_FIRST);
            } else {
                // 非第一次审核通过的职位----2
                $jobEditLog = BaseJobEdit::find()
                    ->where([
                        'job_id' => $this->jobId,
                    ])
                    ->select([
                        'id',
                        'edit_content as editContent',
                    ])
                    ->orderBy('id desc')
                    ->asArray()
                    ->one();

                $handleAfterMessage = json_decode($jobEditLog['editContent'], true);
                foreach ($handleAfterMessage as $k => $item) {
                    if (in_array($k, [
                        'duty',
                        'requirement',
                        'remark',
                        'file_ids',
                    ])) {
                        $jobInfo->$k = $item ?: '';
                    }
                }
                //写入审核通过日志
                $this->log(BaseJobLog::TYPE_AUDIT_AGREE);
            }
            //审核通过公共修改字段
            $jobInfo->audit_status      = BaseJob::AUDIT_STATUS_PASS_AUDIT;
            $jobInfo->release_time      = CUR_DATETIME;
            $jobInfo->refresh_time      = CUR_DATETIME;
            $jobInfo->refresh_date      = CUR_DATE;
            $jobInfo->real_refresh_time = TimeHelper::ZERO_TIME;
        } else {
            if (!$this->params['opinion'] && $this->companyInfo->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES) {
                throw new Exception('审核拒绝时，审核处理意见不能为空！');
            }
            //审核拒绝
            $jobInfo->audit_status = BaseJob::AUDIT_STATUS_REFUSE_AUDIT;
            if ($this->jobInfo->is_article == BaseJob::IS_ARTICLE_NO && $this->oldJobInfo->status == BaseJob::STATUS_WAIT) {
                //因为需要返还资源实际就是没有扣资源
                $jobInfo->is_consume_release = BaseJob::IS_CONSUME_RELEASE_NO;
            }
            //写入审核拒绝日志
            $this->log(BaseJobLog::TYPE_AUDIT_REFUSE);
        }

        //无论审核通过或失败都保存一下审核人与审核人名称
        $jobInfo->audit_admin_id   = $this->params['userId'];
        $jobInfo->audit_admin_name = $this->params['username'];
        if (!$jobInfo->save()) {
            throw new Exception('审核失败');
        }
        //职位新的数据赋值
        $this->jobInfo = $jobInfo;
        if ($isFirstPublish) {
            $title       = '关注单位职位发布通知';
            $content     = '您关注的单位"' . $this->companyInfo->full_name . '"发布了一条新职位："' . $jobInfo->name . '"，快去看看吧～';
            $link_key    = BaseMemberMessage::LINK_TYPE_JOB_DETAIL;
            $link_params = [
                'id' => $jobInfo->id,
            ];
            BaseMemberMessage::send(BaseCompanyCollect::collectMemberIds($this->companyInfo->id),
                BaseMemberMessage::TYPE_RESUME_SYSTEM, $title, $content, $link_key, $link_params);
        }
        //这个时候无论是通过还是拒绝 直接删除job_edit记录
        BaseJobEdit::deleteAll(['job_id' => $this->jobId]);
        //写入日志
        $this->handleLog();
        // 看看本次审核拒绝是否需要归还扣减资源
        if ($this->params['auditStatus'] == BaseJob::AUDIT_STATUS_REFUSE_AUDIT && $this->jobInfo->is_article == BaseJob::IS_ARTICLE_NO && $this->oldJobInfo->status == BaseJob::STATUS_WAIT && $this->oldJobInfo->is_consume_release == BaseJob::IS_CONSUME_RELEASE_YES) {
            $companyPackageApplication = new CompanyPackageApplication();
            $handleType                = BaseCompanyPackageChangeLog::HANDLE_TYPE_JOB_REFUSE;
            $remark                    = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[$handleType];
            $companyPackageApplication->jobRefuse($this->companyInfo->id, 1, $remark);
        }
        //执行职位后置逻辑
        $this->after();

        return true;
    }

    /**
     * 处理所有后置逻辑
     * @throws Exception
     */
    private function after()
    {
        $this->updateStatInfo();
        $this->runAutoColumnAfter();
        $this->updateUuid();
        if ($this->jobInfo->announcement_id) {
            (new AfterService())->setPlatform($this->operationPlatform)
                ->run($this->jobInfo->announcement_id);
        }
    }

    /**
     * 处理审核日志写入
     * @return void
     * @throws Exception
     * @throws \yii\base\NotSupportedException
     */
    private function handleLog()
    {
        //操作动作入表
        $handleBefore = [
            '审核状态' => BaseJob::JOB_AUDIT_STATUS_NAME[BaseJob::AUDIT_STATUS_WAIT_AUDIT],
        ];
        $handleAfter  = [
            '审核状态' => BaseJob::JOB_AUDIT_STATUS_NAME[$this->jobInfo->audit_status],
        ];
        if ($this->params['opinion']) {
            $handleAfter['审核原因'] = $this->params['opinion'];
        }
        $jobHandleLog = [
            'add_time'        => CUR_DATETIME,
            'job_id'          => $this->jobId,
            'handle_type'     => BaseJobHandleLog::HANDLE_TYPE_AUDIT,
            'handler_type'    => $this->params['platformType'],
            'handler_id'      => $this->params['userId'],
            'handler_name'    => $this->params['username'],
            'handle_before'   => json_encode($handleBefore),
            'handle_after'    => json_encode($handleAfter),
            'ip'              => IpHelper::getIpInt(),
            'announcement_id' => $this->jobInfo->announcement_id ?: 0,
        ];
        BaseJobHandleLog::createInfo($jobHandleLog);
    }
}