<?php
/**
 * create user：shannon
 * create time：2025/4/21 上午9:09
 */
namespace common\service\v2\announcement;

use admin\models\RuleAnnouncement;
use common\base\models\BaseAdmin;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementAreaRelation;
use common\base\models\BaseAnnouncementBoshihou;
use common\base\models\BaseAnnouncementEdit;
use common\base\models\BaseAnnouncementEducationRelation;
use common\base\models\BaseAnnouncementExtra;
use common\base\models\BaseAnnouncementLog;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyFeatureTag;
use common\base\models\BaseCompanyFeatureTagRelation;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseCompanyStatData;
use common\base\models\BaseFile;
use common\base\models\BaseJob;
use common\base\models\BaseJobTemp;
use common\base\models\BaseMember;
use common\base\models\BaseShowcase;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use common\helpers\ValidateHelper;
use common\libs\BadWordCheck;
use common\libs\Cache;
use common\libs\ColumnAuto\AnnouncementAutoClassify;
use common\service\CommonService;
use common\service\v2\job\TempDeleteService;
use common\service\zhaoPinHuiColumn\SpecialActivityService;
use common\service\v2\job\AddService;
use common\service\v2\job\EditService;
use queue\AfterAnnouncementUpdateJob;
use queue\Producer;
use yii\base\Exception;
use Yii;

/**
 * 公告--公告--公告
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class BaseService extends CommonService
{
    /** form表单参数 */
    protected $params;
    /** 登录用户信息 */
    protected $loginInfo;
    /** 当前操作的公告ID */
    protected $announcementId;
    /** 当前操作的文章ID */
    protected $articleId;
    /**
     * 当前操作的公告信息
     * @var BaseAnnouncement
     */
    protected $announcementInfo;
    /**
     * 当前操作的文章信息
     * @var BaseArticle
     */
    protected $articleInfo;
    /** 是否进过审核 */
    protected $isAudit = false;
    /**
     * 原始公告信息
     * @var BaseAnnouncement
     */
    protected $oldAnnouncementInfo;
    /**
     * 原始公告文章信息
     * @var BaseArticle
     */
    protected $oldArticleInfo;
    /**
     * 单位信息
     * @var BaseCompany
     */
    protected $companyInfo;

    /**
     * 单位套餐
     * @var BaseCompanyPackageConfig
     */
    protected $companyPackageConfigModel;

    /** 添加编辑时候的一些数据处理 */
    protected $jobIds         = [];//正式职位Id集合
    protected $jobData        = [];//正式职位数据信息集合
    protected $jobTempIds     = [];//临时职位Id集合
    protected $jobTempData    = [];//临时职位数据信息集合
    protected $jobAndTempIds  = [];//正式表也有临时职位Id集合
    protected $jobAndTempData = [];//正式表也有临时职位数据信息集合

    const SUBMIT_TYPE_BUTTON_SAVE    = 1;//保存
    const SUBMIT_TYPE_BUTTON_PUBLISH = 2;//发布
    /** 定义公告及公告下职位审核的字段状态 */
    protected $isAuditAnnouncementCentent = false;//公告内容
    protected $isAuditAnnouncementFileIds = false;//公告职位附件

    /**
     * @throws Exception
     */
    protected function __construct()
    {
        parent::__construct();
        if (!$this->operationPlatform) {
            switch (PLATFORM) {
                case 'PC':
                    $this->operationPlatform = self::PLATFORM_WEB_COMPANY;
                    break;
                case 'ADMIN':
                    $this->operationPlatform = self::PLATFORM_ADMIN;
                    break;
                default:
                    $this->operationPlatform = self::PLATFORM_TIMER;
                    break;
            }
        }
    }

    /**
     * 设置职位信息
     * @param $announcementId
     * @return $this
     * @throws MessageException
     */
    protected function setAnnouncement($announcementId)
    {
        $this->announcementId   = $announcementId;
        $this->announcementInfo = BaseAnnouncement::findOne(['id' => $announcementId]);
        $this->articleId        = $this->announcementInfo->article_id;
        $this->articleInfo      = BaseArticle::findOne($this->articleId);

        if (!$this->announcementInfo) {
            throw new \Exception('公告信息不存在');
        }
        if (!$this->articleInfo) {
            throw new \Exception('文章信息不存在');
        }

        $this->setCompany($this->announcementInfo->company_id);

        return $this;
    }

    /**
     * 设置单位信息
     * @param $companyId
     * @throws MessageException
     */
    protected function setCompany($companyId)
    {
        if (!$this->companyInfo || $this->companyInfo->id != $companyId) {
            $this->companyInfo = BaseCompany::findOne($companyId);
        }
        if (!$this->companyInfo) {
            throw new \Exception('单位信息不存在');
        }

        return $this;
    }

    /**
     * 操作日志
     * @param $type
     */
    protected function log($type)
    {
        BaseAnnouncementLog::addLog([
            'createType'     => $this->params['platformType'],
            'userId'         => $this->params['userId'],
            'createName'     => $this->operationPlatform == self::PLATFORM_WEB_COMPANY ? $this->companyInfo->full_name . '-' . $this->params['username'] : $this->params['username'],
            'type'           => $type,
            'announcementId' => $this->announcementId ?: ($this->announcementInfo->id ?: 0),
        ]);

        return $this;
    }

    public function afterAnnouncementUpdateJob()
    {
        $this->updateAnnouncementPiFlag();
        $this->updateAnnouncementMiniAppType();
        $this->updateJobAnnouncementAmount();
        $this->updateAnnouncementBoShiHouTable();
        $this->updateJobAnnouncementRelation();
        $this->updateStatAnnouncementCount();

        $this->runAutoColumnAfter();
    }

    /**
     * 初始化登录的账号信息、单位信息
     * @throws Exception
     */
    protected function initInfo()
    {
        //根据平台初始化登录账号信息
        switch ($this->operationPlatform) {
            case self::PLATFORM_WEB_COMPANY:
                $id = Yii::$app->user->id;
                //获取登录账号信息
                $this->loginInfo = BaseMember::findOne($id);
                //获取单位信息
                $companyMemberInfo         = BaseCompanyMemberInfo::findOne(['member_id' => $id]);
                $this->companyInfo         = BaseCompany::findOne($companyMemberInfo->company_id);
                $this->params['companyId'] = $this->companyInfo->id;
                //创建类型
                $this->params['createType']   = BaseAnnouncement::CREATE_TYPE_SELF;
                $this->params['platformType'] = BaseAnnouncementEdit::EDITOR_TYPE_COMPANY;
                //登录人ID
                $this->params['userId'] = $this->loginInfo->id;
                //登录人名称
                $this->params['username'] = $this->loginInfo->username;
                $this->params['userType'] = BaseAnnouncement::APPLY_ID_TYPE_COMPANY;
                break;
            case self::PLATFORM_ADMIN:
                if (isset($this->params['companyId']) && $this->params['companyId'] > 0 && empty($this->companyInfo)) {
                    $this->setCompany($this->params['companyId']);
                }
                $id = Yii::$app->user->id;
                //获取登录账号信息
                $this->loginInfo = BaseAdmin::findOne($id);
                //获取单位信息
                //$this->companyInfo = BaseCompany::findOne($this->params['companyId']);
                //创建类型
                $this->params['createType']   = BaseAnnouncement::CREATE_TYPE_AGENT;
                $this->params['platformType'] = BaseAnnouncementEdit::EDITOR_TYPE_PLATFORM;
                //登录人ID
                $this->params['userId'] = $this->loginInfo->id;
                //登录人名称
                $this->params['username'] = $this->loginInfo->name;
                $this->params['userType'] = BaseAnnouncement::APPLY_ID_TYPE_ADMIN;
                break;
            case self::PLATFORM_TIMER:
                if (isset($this->params['companyId']) && $this->params['companyId'] > 0 && empty($this->companyInfo)) {
                    $this->setCompany($this->params['companyId']);
                }
                //创建类型
                $this->params['createType']   = 0;
                $this->params['platformType'] = 0;
                //登录人ID
                $this->params['userId'] = 0;
                //登录人名称
                $this->params['username'] = '系统';
                $this->params['userType'] = 0;
                break;
            default:
                break;
        }
    }

    /**
     * 公告数据公共校验
     * @param
     * @throws MessageException
     * @throws Exception
     */
    protected function dataVerify($isAdd = true)
    {
        if (!$this->params['title']) {
            throw new Exception('公告标题不能为空');
        }
        //公告标题可不可用
        //$this->checkTitle();
        if (!$this->params['content']) {
            throw new Exception('公告详情不能为空');
        }
        if (!$this->params['jobIds']) {
            throw new Exception('请添加职位');
        }
        //这里直接处理一下职位jobids 传递过来的数据json转数组
        if (!is_array($this->params['jobIds'])) {
            $this->params['jobIds'] = json_decode($this->params['jobIds'], true);
            //--后改增加的有点冗余判断 没有删除上面提示
            if (count($this->params['jobIds']) <= 0) {
                throw new Exception('请添加职位');
            }
        }
        //报名方式与通知地址不可同时填写
        if ($this->params['applyType'] && $this->params['extraNotifyAddress']) {
            throw new Exception('报名方式与投递通知邮箱不可同时填写');
        }
        //检查职位附件记录
        if ($this->params['fileIds']) {
            $this->checkJobAppendix();
        }
        // 企业的
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            if (($isAdd || $this->oldAnnouncementInfo->status == BaseAnnouncement::STATUS_STAGING) && !$this->params['periodDate']) {
                throw new Exception('公告有效期不能为空');
            }
        }
        // 运营后台添加的
        if ($this->operationPlatform == self::PLATFORM_ADMIN) {
            if (!$this->params['homeColumnId']) {
                throw new Exception('所属栏目不能为空');
            }
            if (!$this->params['companyId']) {
                throw new Exception('请选择单位');
            }
            if ($this->params['comboAttribute'] || $this->params['overseasAttribute']) {
                $attribute     = array_unique(ArrayHelper::merge($this->params['comboAttribute'],
                    $this->params['overseasAttribute']));
                $attributeData = [
                    'attribute'                => $attribute,
                    'indexTopEndTime'          => $this->params['indexTopEndTime'] ?: '',
                    'columnTopEndTime'         => $this->params['columnTopEndTime'] ?: '',
                    'doctorPushEndTime'        => $this->params['doctorPushEndTime'] ?: '',
                    'overseasIndexTopEndTime'  => $this->params['overseasIndexTopEndTime'] ?: '',
                    'overseasColumnTopEndTime' => $this->params['overseasColumnTopEndTime'] ?: '',
                ];
                $attributeSet  = BaseArticleAttribute::formatAttributeList($attributeData);
            }
            /**
             * 勾选置顶属性结束时间不得大于公告截止日期
             */
            if ($this->params['periodDate'] && strtotime($this->params['periodDate']) > 0 && isset($attributeSet) && $this->params['comboAttribute']) {
                foreach ($attributeSet as $item) {
                    if ($item['expireTime'] > $this->params['periodDate']) {
                        throw new Exception(BaseArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_LIST[$item['type']] . '-文档属性结束时间不得大于公告截止时间');
                    }
                }
            }
            if ($this->companyInfo->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES) {
                if ($this->params['deliveryWay'] == BaseJob::DELIVERY_WAY_EMAIL_LINK) {
                    if (empty($this->params['applyType'])) {
                        throw new Exception('报名方式没有勾选');
                    }
                    $applyTypeArr = explode(',', $this->params['applyType']);
                    $isEmail      = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr);
                    //校验
                    if ($isEmail) {
                        BaseJob::checkEmailApplyAddress($this->params['applyAddress']);
                        $this->params['deliveryWay'] = BaseJob::DELIVERY_WAY_EMAIL;
                    } else {
                        if (!ValidateHelper::isUrl($this->params['applyAddress'])) {
                            throw new Exception('单位报名网址格式错误');
                        }
                        $this->params['deliveryWay'] = BaseJob::DELIVERY_WAY_LINK;
                    }
                    $this->params['extraNotifyAddress'] = '';
                } else {//deliveryWay=1
                    $this->params['applyType']    = '';
                    $this->params['applyAddress'] = '';
                }
                if ($this->params['deliveryWay'] == 0) {
                    $this->params['deliveryType'] = 0;
                } elseif ($this->params['deliveryWay'] == BaseJob::DELIVERY_WAY_LINK) {
                    $this->params['deliveryType'] = BaseJob::DELIVERY_TYPE_OUTSIDE;
                } else {
                    $this->params['deliveryType'] = BaseJob::DELIVERY_TYPE_INSIDE;
                }
            } else {
                //非合作进来就删除deliveryWay
                unset($this->params['deliveryWay']);
                if ($this->params['extraNotifyAddress']) {
                    throw new Exception('非合作单位不支持填写邮件通知地址');
                }
                ///非合作单位报名方式必填
                if (!$this->params['applyType'] || !$this->params['applyAddress']) {
                    throw new Exception('报名方式没有勾选或者投递地址为空');
                }

                if ($this->params['applyType']) {
                    if (empty($this->params['applyAddress'])) {
                        throw new Exception('投递地址不能为空');
                    }
                    $applyTypeArr = explode(',', $this->params['applyType']);
                    $isEmail      = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr);
                    //校验
                    if ($isEmail) {
                        BaseJob::checkEmailApplyAddress($this->params['applyAddress']);
                        $this->params['deliveryWay'] = BaseJob::DELIVERY_WAY_EMAIL;
                    } else {
                        if (!ValidateHelper::isUrl($this->params['applyAddress'])) {
                            throw new Exception('单位报名网址格式错误');
                        }
                        $this->params['deliveryWay'] = BaseJob::DELIVERY_WAY_LINK;
                    }
                    $this->params['extraNotifyAddress'] = '';
                } else {
                    $this->params['applyType']    = '';
                    $this->params['applyAddress'] = '';
                    $this->params['deliveryWay']  = BaseAnnouncement::DELIVERY_WAY_UN;
                }
                if ($this->params['deliveryWay'] == BaseAnnouncement::DELIVERY_WAY_UN) {
                    $this->params['deliveryType'] = BaseAnnouncement::DELIVERY_TYPE_UN;
                } else {
                    $this->params['deliveryType'] = BaseAnnouncement::DELIVERY_TYPE_OUTSIDE;
                }
            }
        } else {
            //单位端公告层面直接就是没有的
            if ($isAdd) {
                $this->params['applyType']          = '';
                $this->params['applyAddress']       = '';
                $this->params['extraNotifyAddress'] = '';
                $this->params['deliveryWay']        = BaseAnnouncement::DELIVERY_WAY_UN;
                $this->params['deliveryType']       = BaseAnnouncement::DELIVERY_TYPE_UN;
            } else {
                $this->params['applyType']          = $this->oldAnnouncementInfo->apply_type;
                $this->params['applyAddress']       = $this->oldAnnouncementInfo->apply_address;
                $this->params['extraNotifyAddress'] = $this->oldAnnouncementInfo->extra_notify_address;
                $this->params['deliveryWay']        = $this->oldAnnouncementInfo->delivery_way;
                $this->params['deliveryType']       = $this->oldAnnouncementInfo->delivery_type;
            }
        }

        //检查通知邮箱的格式
        if ($this->params['extraNotifyAddress']) {
            BaseJob::checkEmailApplyAddress($this->params['extraNotifyAddress']);
        }
        // 注意捆绑到 jobProcess之前
        if (!$isAdd && $this->oldAnnouncementInfo->status != BaseAnnouncement::STATUS_STAGING && $this->params['deliveryType'] > 0 && $this->oldAnnouncementInfo->delivery_type > 0 && $this->oldAnnouncementInfo->delivery_type != $this->params['deliveryType']) {
            throw new Exception('编辑公告报名方式导致投递类型发生变更，请注意报名方式的修改规则！');
        }
        //在这里处理一下我们的公告下职位列表的整理
        $this->processJob();
        // 最后检查敏感词
        // 检查内容是否有敏感词
        $this->checkWord();
    }

    /**
     * 保存公告
     * @param $isAdd
     */
    protected function saveTable($isAdd = true)
    {
        if ($isAdd) {
            $announcementModel               = new BaseAnnouncement();
            $articleModel                    = new BaseArticle();
            $announcementModel->create_type  = $this->params['createType'];
            $announcementModel->creator_id   = $this->params['userId'];
            $announcementModel->creator_name = $this->params['username'];
            if ($this->params['submitType'] == self::SUBMIT_TYPE_BUTTON_PUBLISH) {
                $announcementModel->is_consume_release = $this->operationPlatform == self::PLATFORM_ADMIN ? BaseAnnouncement::IS_CONSUME_RELEASE_NO : BaseAnnouncement::IS_CONSUME_RELEASE_YES;
                $announcementModel->audit_status       = BaseAnnouncement::STATUS_AUDIT_AWAIT;
                $announcementModel->apply_id           = $this->params['userId'];
                $announcementModel->apply_name         = $this->params['username'];
                $announcementModel->apply_id_type      = $this->params['userType'];
                $articleModel->apply_audit_time        = CUR_DATETIME;
            } else {
                $announcementModel->is_consume_release = BaseAnnouncement::IS_CONSUME_RELEASE_NO;
                $announcementModel->audit_status       = BaseAnnouncement::STATUS_AUDIT_STAGING;
            }
            $announcementModel->status = BaseAnnouncement::STATUS_STAGING;
            $articleModel->status      = BaseArticle::STATUS_STAGING;
            $articleModel->is_delete   = BaseArticle::IS_DELETE_NO;
            $articleModel->is_show     = BaseArticle::IS_SHOW_YES;
            $articleModel->type        = BaseArticle::TYPE_ANNOUNCEMENT;
        } else {
            $announcementModel = BaseAnnouncement::findOne($this->announcementId);
            $articleModel      = BaseArticle::findOne($this->articleId);
            if ($this->isAudit) {
                $announcementModel->audit_status  = BaseAnnouncement::STATUS_AUDIT_AWAIT;
                $announcementModel->apply_id      = $this->params['userId'];
                $announcementModel->apply_name    = $this->params['username'];
                $announcementModel->apply_id_type = $this->params['userType'];
                $articleModel->apply_audit_time   = CUR_DATETIME;
                if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY && $announcementModel->status == BaseAnnouncement::STATUS_STAGING && $announcementModel->is_consume_release == BaseAnnouncement::IS_CONSUME_RELEASE_NO) {
                    $announcementModel->is_consume_release = BaseJob::IS_CONSUME_RELEASE_YES;
                }
            }
        }

        $articleModel->tag_ids         = $this->params['tagIds'] ?: '';
        $articleModel->recommend_ids   = $this->params['recommendIds'] ?: '';
        $articleModel->cover_thumb     = $this->params['coverThumb'] ?: '';
        $articleModel->seo_description = $this->params['seoDescription'] ?: '';
        $articleModel->seo_keywords    = $this->params['seoKeywords'] ?: '';
        $articleModel->title           = $this->params['title'] ?: '';
        if (!$this->isAuditAnnouncementCentent) {
            $articleModel->content = $this->params['content'] ?: '';
        }
        $articleModel->home_column_id      = $this->params['homeColumnId'];
        $articleModel->home_sub_column_ids = $this->params['homeSubColumnIds'] ?: '';
        if (!$articleModel->save()) {
            throw new Exception($articleModel->getFirstErrorsMessage());
        }
        $this->articleId = $articleModel->id;

        $announcementModel->major_ids                   = '';//没有这个直接写空
        $announcementModel->article_id                  = $articleModel->id;
        $announcementModel->relation_company_ids        = $this->params['relationCompanyIds'] ?: '';
        $announcementModel->apply_type                  = $this->params['applyType'] ?: '';
        $announcementModel->apply_address               = $this->params['applyAddress'] ?: '';
        $announcementModel->template_id                 = $this->params['templateId'] ?: BaseAnnouncement::TEMPLATE_ORDINARY;
        $announcementModel->title                       = $this->params['title'] ?: '';
        $announcementModel->company_id                  = $this->params['companyId'] ?: 0;
        $announcementModel->is_announcement_cooperation = $this->companyInfo->is_cooperation;
        $announcementModel->member_id                   = $this->companyInfo->id;
        $announcementModel->period_date                 = $this->params['periodDate'] ?: TimeHelper::ZERO_TIME;
        $announcementModel->delivery_type               = $this->params['deliveryType'] ?: 0;
        $announcementModel->delivery_way                = $this->params['deliveryWay'] ?: 0;
        $announcementModel->extra_notify_address        = $this->params['extraNotifyAddress'] ?: '';
        $announcementModel->is_attachment_notice        = $this->params['isAttachmentNotice'] ?: BaseAnnouncement::IS_ATTACHMENT_NOTICE_NO;
        $announcementModel->sub_title                   = $this->params['subTitle'] ?: '';
        $announcementModel->highlights_describe         = $this->params['highlightsDescribe'] ?: '';
        $announcementModel->background_img_file_id      = $this->params['backgroundImgFileId'] ?: '';
        $announcementModel->background_img_file_id_2    = $this->params['backgroundImgFileId2'] ?: '';
        $announcementModel->background_img_file_id_3    = $this->params['backgroundImgFileId3'] ?: '';
        $announcementModel->background_img_file_type    = $this->params['backgroundImgFileType'] ?: BaseAnnouncement::BACKGROUND_IMG_FILE_TYPE_DEFAULT;
        $announcementModel->activity_job_content        = $this->params['activityJobContent'] ?: '';
        //fileIds需要进入审核时候不允许直接写入
        if (!$this->isAuditAnnouncementFileIds) {
            $announcementModel->file_ids = $this->params['fileIds'] ?: '';
        }
        //判断是否非合作单位，处理地址隐藏
        if ($this->companyInfo->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES) {
            $announcementModel->address_hide_status = BaseAnnouncement::ADDRESS_HIDE_STATUS_NO;
        } else {
            $announcementModel->address_hide_status = $this->params['addressHideStatus'] ?: BaseAnnouncement::ADDRESS_HIDE_STATUS_NO;
        }
        if (!$announcementModel->save()) {
            throw new Exception($announcementModel->getFirstErrorsMessage());
        }
        $this->announcementId   = $announcementModel->id;
        $this->announcementInfo = $announcementModel;
        $this->articleInfo      = $articleModel;
        // 运营添加
        if ($this->operationPlatform == self::PLATFORM_ADMIN) {
            //拼凑栏目属性和时间数组，进行添加
            if ($this->params['comboAttribute'] || $this->params['overseasAttribute']) {
                $attribute      = array_unique(ArrayHelper::merge($this->params['comboAttribute'],
                    $this->params['overseasAttribute']));
                $attributeData  = [
                    'attribute'                => $attribute,
                    'indexTopEndTime'          => $this->params['indexTopEndTime'] ?: '',
                    'columnTopEndTime'         => $this->params['columnTopEndTime'] ?: '',
                    'doctorPushEndTime'        => $this->params['doctorPushEndTime'] ?: '',
                    'overseasIndexTopEndTime'  => $this->params['overseasIndexTopEndTime'] ?: '',
                    'overseasColumnTopEndTime' => $this->params['overseasColumnTopEndTime'] ?: '',
                ];
                $comboAttribute = BaseArticleAttribute::formatAttributeList($attributeData);
                BaseArticleAttribute::createAttribute($this->articleId, $comboAttribute);
            } else {
                BaseArticleAttribute::deleteAttribute($this->articleId);
            }
        }
        //增加公告附属表数据
        BaseAnnouncementExtra::insertData([
            'announcement_id' => $announcementModel->id,
            'company_id'      => $this->params['companyId'],
        ]);

        // 更新公告关联的活动
        if ($this->operationPlatform == self::PLATFORM_ADMIN) {
            SpecialActivityService::multipleActivityRelationSingleAnnouncement($announcementModel->company_id,
                $this->params['activityAnnouncement'] ?? '', $announcementModel->id);
        }
    }

    /**
     * 保存公告下职位
     * @throws Exception
     */
    protected function saveJobTable()
    {
        if ($this->params['submitType'] == self::SUBMIT_TYPE_BUTTON_PUBLISH) {
            $auditStatus = BaseJob::AUDIT_STATUS_WAIT_AUDIT;
        } else {
            $auditStatus = BaseJob::AUDIT_STATUS_WAIT;
        }
        //新增的职位
        foreach ($this->jobTempData as $jobTempItem) {
            //这时候直接同步写入正式表--新增职位那我就是要确定是发布还是保存
            //我要让他走到新增职位的一套 我要将数据处理成新增职位数据格式
            $jobTempItem['auditStatus']    = $auditStatus;
            $jobTempItem['companyId']      = $this->companyInfo->id;
            $jobTempItem['announcementId'] = $this->announcementId;
            (new AddService())->setPlatform($this->operationPlatform)
                ->setParams($jobTempItem)
                ->run();
        }
        //有临时职位也有正式职位的数据处理
        foreach ($this->jobAndTempData as $jobAndTempItem) {
            //这个要看是否是需要审核的
            //那么我们让他走只为编辑流程
            $jobAndTempItem['auditStatus']    = $auditStatus;
            $jobAndTempItem['companyId']      = $this->companyInfo->id;
            $jobAndTempItem['announcementId'] = $this->announcementId;
            (new EditService())->setPlatform($this->operationPlatform)
                ->setParams($jobAndTempItem)
                ->run();
        }
        //有正式数据的也会存在一种情况就是先保存在发布  这时候正式表是有数据的 这个时候也需要处理待发布编辑中的数据去到审核
        if ($this->params['submitType'] == self::SUBMIT_TYPE_BUTTON_PUBLISH) {
            foreach ($this->jobData as $jobItem) {
                if ($jobItem['status'] == BaseJob::STATUS_WAIT && $jobItem['auditStatus'] != BaseJob::AUDIT_STATUS_WAIT_AUDIT) {
                    //注意这里重置了auditStatus
                    $jobItem['auditStatus']    = $auditStatus;
                    $jobItem['companyId']      = $this->companyInfo->id;
                    $jobItem['announcementId'] = $this->announcementId;
                    (new EditService())->setPlatform($this->operationPlatform)
                        ->setParams($jobItem)
                        ->run();
                }
            }
        }
        //最后把临时职位都删除掉
        $id = ArrayHelper::merge($this->jobTempIds, $this->jobAndTempIds);
        if ($id) {
            (new TempDeleteService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->setParams(['id' => $id])
                ->run();
        }
    }

    /**
     * 检查公告标题是否可用
     * @throws Exception
     */
    private function checkTitle()
    {
        if ($this->announcementId) {
            $info = BaseAnnouncement::find()
                ->where([
                    'title'  => $this->params['title'],
                    'status' => BaseAnnouncement::STATUS_DELETE,
                ])
                ->andWhere([
                    '<>',
                    'id',
                    $this->announcementId,
                ])
                ->asArray()
                ->one();
        } else {
            $info = BaseAnnouncement::find()
                ->select('id')
                ->where([
                    'title'  => $this->params['title'],
                    'status' => BaseAnnouncement::STATUS_DELETE,
                ])
                ->asArray()
                ->one();
        }
        if ($info['id']) {
            throw new Exception('公告标题不可用');
        }
    }

    /**
     * 检查一些字段内容是否还有敏感词
     * @throws Exception
     */
    private function checkWord()
    {
        // 首先找到公告的内容
        $announcementContent = $this->params['content'];
        // 去掉html内容
        $announcementContent = strip_tags($announcementContent);
        // 然后找到职位的内容
        $tmpJobList = ArrayHelper::merge($this->jobTempData, $this->jobAndTempData);
        $content    = $announcementContent;
        // 上面的内容一起塞到content里面
        foreach ($tmpJobList as $job) {
            $content .= $job['duty'] . $job['requirement'] . $job['remark'];
        }

        // 去掉空格换行
        $content = str_replace([
            "\r\n",
            "\r",
            "\n",
            " ",
            PHP_EOL,
        ], "", $content);

        // 检查内容
        $checkModel = new BadWordCheck();
        $checkModel->check($content);

        $badWord = $checkModel->badWord;

        $message = '';
        if (count($badWord) > 0) {
            // 循环去查询实际上是属于哪字段
            foreach ($badWord as $item) {
                $match                    = false;
                $matchAnnouncementContent = str_replace([
                    "\r\n",
                    "\r",
                    "\n",
                    " ",
                    PHP_EOL,
                ], "", $announcementContent);
                if (strpos($matchAnnouncementContent, $item) !== false) {
                    $message .= '公告内容中含有敏感词:' . $item . ',';
                    $match   = true;
                } else {
                    foreach ($tmpJobList as $job) {
                        $matchJobDuty        = str_replace([
                            "\r\n",
                            "\r",
                            "\n",
                            " ",
                            PHP_EOL,
                        ], "", $job['duty']);
                        $matchJobRequirement = str_replace([
                            "\r\n",
                            "\r",
                            "\n",
                            " ",
                            PHP_EOL,
                        ], "", $job['requirement']);
                        $matchJobRemark      = str_replace([
                            "\r\n",
                            "\r",
                            "\n",
                            " ",
                            PHP_EOL,
                        ], "", $job['remark']);

                        if (strpos($matchJobDuty, $item) !== false) {
                            $message .= '职位id:' . $job['id'] . '岗位职责中含有敏感词:' . $item . ',';
                            $match   = true;
                        }
                        if (strpos($matchJobRequirement, $item) !== false) {
                            $message .= '职位id:' . $job['id'] . '任职要求中含有敏感词:' . $item . ',';
                            $match   = true;
                        }
                        if (strpos($matchJobRemark, $item) !== false) {
                            $message .= '职位id:' . $job['id'] . '其他说明中含有敏感词:' . $item . ',';
                            $match   = true;
                        }
                    }
                }

                if (!$match) {
                    $message .= '内容中含有敏感词:' . $item . ',';
                }
            }

            // 去掉最后一个逗号
            $message = substr($message, 0, -1);

            throw new Exception($message);
        }
    }

    /**
     * 检查职位附件记录是否存在
     * @throws Exception
     */
    private function checkJobAppendix()
    {
        $fileIdArr = explode(',', $this->params['fileIds']);
        $ids       = BaseFile::find()
            ->select('id')
            ->where(['id' => $fileIdArr])
            ->asArray()
            ->column();
        if (!$ids) {
            throw new Exception('职位附件记录不存在');
        }
    }

    /**
     * 整理一下公告下职位列表提交的数据
     * $this->params['jobIds']=[{'id':0,'isTemp':2,'jobId':20122},{'id':10,'isTemp':1,'jobId':20122},{'id':100,'isTemp':1,'jobId':0}]
     * @throws Exception
     */
    private function processJob()
    {
        // $jobIds $tempJobIds jobAndTempIds的整理与赋值
        foreach ($this->params['jobIds'] as $item) {
            if ($item['id'] == 0 && $item['isTemp'] == BaseJobTemp::IS_TEMP_NO && $item['jobId'] > 0) {
                //这个时候就是正式表的原始职位
                $this->jobIds[] = $item['jobId'];
            }
            if ($item['id'] > 0 && $item['isTemp'] == BaseJobTemp::IS_TEMP_YES && $item['jobId'] > 0) {
                //这个时候就是正式表的原始职位，临时表也有临时职位数据
                $this->jobAndTempIds[] = $item['id'];
            }
            if ($item['id'] > 0 && $item['isTemp'] == BaseJobTemp::IS_TEMP_YES && $item['jobId'] == 0) {
                //这个时候只有有临时职位数据，即是新增的职位数据
                $this->jobTempIds[] = $item['id'];
            }
        }
        if (count($this->params['jobIds']) != count($this->jobIds) + count($this->jobAndTempIds) + count($this->jobTempIds)) {
            throw new Exception('职位数据有误,请联系开发人员进行处理！');
        }
        // 正式表数据不用动 暂时不做处理 $this->jobData
        // 临时表数据处理
        $select = [
            'id',
            'company_id as companyId',
            'name',
            'period_date as periodDate',
            'code',
            'job_category_id as jobCategoryId',
            'education_type as educationType',
            'major_id as majorId',
            'nature_type as natureType',
            'is_negotiable as isNegotiable',
            'wage_type as wageType',
            'min_wage as minWage',
            'max_wage as maxWage',
            'experience_type as experienceType',
            'age_type as ageType',
            'min_age as minAge',
            'max_age as maxAge',
            'title_type as titleType',
            'political_type as politicalType',
            'abroad_type as abroadType',
            'amount',
            'department',
            'province_id as provinceId',
            'city_id as cityId',
            'district_id as districtId',
            'address',
            'welfare_tag as welfareTag',
            'duty',
            'requirement',
            'remark',
            'apply_type as applyType',
            'apply_address as applyAddress',
            'add_time as addTime',
            'delivery_limit_type as deliveryLimitType',
            'delivery_type as deliveryType',
            'extra_notify_address as extraNotifyAddress',
            'delivery_way as deliveryWay',
            'establishment_type as establishmentType',
            'announcement_id as announcementId',
            'is_establishment as isEstablishment',
            'establishment_type as establishmentType',
        ];
        if ($this->jobTempIds) {
            $jobTempSelect     = $select;
            $jobTempSelect[]   = 'is_temp as isTemp';
            $jobTempSelect[]   = 'job_id as jobId';
            $jobTempSelect[]   = 'contact_id as jobContactId';
            $jobTempSelect[]   = 'contact_synergy_id as jobContactSynergyIds';
            $this->jobTempData = BaseJobTemp::find()
                ->select($jobTempSelect)
                ->where(['id' => $this->jobTempIds])
                ->asArray()
                ->all();
            foreach ($this->jobTempData as &$jobTempDatum) {
                $this->processDeliveryVerify($jobTempDatum);
                $jobTempDatum = $this->processJobPeriodDate($jobTempDatum);
            }
        }
        // 正式表的原始职位，临时表也有临时职位数据
        if ($this->jobAndTempIds) {
            $jobAndTempSelect     = $select;
            $jobAndTempSelect[]   = 'is_temp as isTemp';
            $jobAndTempSelect[]   = 'job_id as jobId';
            $jobAndTempSelect[]   = 'contact_id as jobContactId';
            $jobAndTempSelect[]   = 'contact_synergy_id as jobContactSynergyIds';
            $this->jobAndTempData = BaseJobTemp::find()
                ->select($jobAndTempSelect)
                ->where(['id' => $this->jobAndTempIds])
                ->asArray()
                ->all();
            foreach ($this->jobAndTempData as &$jobAndTempDatum) {
                $this->processDeliveryVerify($jobAndTempDatum);
                $jobAndTempDatum = $this->processJobPeriodDate($jobAndTempDatum);
            }
        }

        // 正式表的原始职位
        if ($this->jobIds) {
            //'file_ids as fileIds',
            $jobSelect     = $select;
            $jobSelect[]   = 'status';
            $jobSelect[]   = 'audit_status as auditStatus';
            $jobSelect[]   = 'id as jobId';
            $this->jobData = BaseJob::find()
                ->select($jobSelect)
                ->where([
                    'id'     => $this->jobIds,
                    'status' => BaseJob::STATUS_WAIT,
                ])
                ->asArray()
                ->all();
            foreach ($this->jobData as &$jobDatum) {
                $this->processDeliveryVerify($jobDatum);
                //添加jobContactId、jobContactSynergyIds
                $contact                          = BaseJob::getJobContact($jobDatum['jobId']);
                $jobDatum['jobContactId']         = $contact['company_member_info_id'];
                $contact_synergy                  = BaseJob::getJobContactSynergy($jobDatum['jobId']);
                $jobDatum['jobContactSynergyIds'] = count($contact_synergy) > 0 ? implode(',',
                    array_column($contact_synergy, 'company_member_info_id')) : '';
            }
        }
    }

    /**
     * 处理投递职位类型验证
     * @throws Exception
     */
    private function processDeliveryVerify($item)
    {
        //判断公告与职位投递相关属性必须填写一个
        if (!$this->params['deliveryType'] && !$item['deliveryType']) {
            throw new Exception('公告与职位ID：' . $item['id'] . '报名方式至少配置一个');
        }
        //接下来判断的是修改了职位，检查自身投递属性是否变更，或者与公告属性发生变更
        //这时候是一个临时职位,则验证职位与原职位或公告投递类型是否一致
        if ($item['jobId'] > 0) {
            $oldJobInfo = BaseJob::findOne($item['jobId']);
            if ($oldJobInfo->status == BaseJob::STATUS_WAIT) {
                return true;
            }
            // 比对投递类型 且就旧职位投递类型不为0
            // 旧类型 新类型
            // JY>0 XY>0
            // JY>0 XY=0
            // JY=0 XY>0
            // JY=0 XY=0
            if ($oldJobInfo->delivery_type > 0) {
                if ($item['deliveryType'] > 0 && $item['deliveryType'] != $oldJobInfo->delivery_type) {
                    throw new Exception('职位名称:' . $item['name'] . '报名方式修改导致投递类型发生变更，请注意报名方式的修改规则！');
                }
                if ($item['deliveryType'] == 0 && $oldJobInfo->delivery_type != $this->params['deliveryType']) {
                    throw new Exception('职位名称:' . $item['name'] . '报名方式清空后导致去获取公告配置报名方式，导致投递类型发生变更，请注意报名方式的修改！');
                }
            } else {
                if ($item['deliveryType'] > 0 && $item['deliveryType'] != $this->params['deliveryType']) {
                    throw new Exception('职位名称:' . $item['name'] . '报名方式新增后，与原始跟随公告配置报名方式不一致，导致投递类型发生变更，请注意报名方式的修改！');
                }
                //这时候比较特殊以前是跟公告现在也是跟公告，那我们就看一下公告自身是否有投递类型就行了，是否变更类型的在最开始就验证了；
            }
        }

        return true;
    }

    /**
     * 处理提交时职位的有效期
     * @throws Exception
     */
    private function processJobPeriodDate($item)
    {
        // 公告和职位都需要是合法的日期
        if (!$this->params['periodDate'] || $this->params['periodDate'] == TimeHelper::ZERO_TIME) {
            return $item;
        }

        if ($item['periodDate'] > $this->params['periodDate']) {
            $item['periodDate'] = $this->params['periodDate'] ?: TimeHelper::ZERO_TIME;
        } else {
            $item['periodDate'] = $item['periodDate'] ?: TimeHelper::ZERO_TIME;
        }

        return $item;
    }

    /**
     * 处理所有后置逻辑
     */
    protected function runAutoColumnAfter()
    {
        //                if (Yii::$app->params['environment'] == 'prod') {
        Producer::afterAnnouncementUpdateJob($this->announcementId);
        //                } else {
        //        $app = new AnnouncementAutoClassify($this->announcementId);
        //        $app->run();
        //        }
    }

    /**
     * 更新公告是否是小程序公告
     */
    protected function updateAnnouncementMiniAppType()
    {
        if ($this->announcementInfo->is_manual_tag = BaseAnnouncement::IS_MANUAL_TAG_NONE) {
            $model     = new RuleAnnouncement();
            $res       = $model->exec($this->announcementId);
            $isMiniApp = $res ? BaseAnnouncement::IS_MINIAPP_YES : BaseAnnouncement::IS_MINIAPP_NO;
        } elseif ($this->announcementInfo->is_manual_tag = BaseAnnouncement::IS_MANUAL_TAG_YES) {
            $isMiniApp = BaseAnnouncement::IS_MINIAPP_YES;
        } else {
            $isMiniApp = BaseAnnouncement::IS_MINIAPP_NO;
        }
        if ($this->announcementInfo->is_miniapp != $isMiniApp) {
            $this->announcementInfo->is_miniapp = $isMiniApp;
            $this->announcementInfo->save();
        }
    }

    /**
     * 更新公告下职位的数量
     * @return string|void
     */
    protected function updateJobAnnouncementAmount()
    {
        //有公告才更新
        $announcementModel                    = BaseAnnouncement::findOne($this->announcementId);
        $announcementModel->online_job_amount = BaseJob::find()
            ->where([
                'announcement_id' => $this->announcementId,
                'status'          => BaseJob::STATUS_ONLINE,
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->count();
        $announcementModel->all_job_amount    = BaseJob::find()
            ->where([
                'announcement_id' => $this->announcementId,
                'status'          => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->count();
        $announcementModel->save();

        //缓存公告招聘人数
        $key    = Cache::ALL_ANNOUNCEMENT_JOB_AMOUNT_KEY . ':' . $this->announcementId;
        $amount = BaseJob::find()
            ->where([
                'announcement_id' => $this->announcementId,
                'status'          => BaseJob::STATUS_ONLINE,
                'is_show'         => BaseJob::IS_SHOW_YES,
                'amount'          => '若干',
            ])
            ->exists() ? '若干' : BaseJob::find()
            ->where([
                'announcement_id' => $this->announcementId,
                'status'          => BaseJob::STATUS_ONLINE,
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->sum('amount');
        Cache::set($key, $amount);
    }

    /**
     * 更新公告附属表的PI标识
     */
    protected function updateAnnouncementPiFlag()
    {
        //先看下是否有附属记录
        $announcementExtraInfo = BaseAnnouncementExtra::findOne(['announcement_id' => $this->announcementId]);
        if (!$announcementExtraInfo) {
            //没有就补一下
            BaseAnnouncementExtra::insertData([
                'announcement_id' => $this->announcementId,
                'company_id'      => $this->announcementInfo->company_id,
            ]);
            $announcementExtraInfo = BaseAnnouncementExtra::findOne(['announcement_id' => $this->announcementId]);
        }
        //看下自身是否有pi属性
        if (BaseArticleAttribute::find()
                ->where([
                    'article_id' => $this->announcementInfo->article_id,
                    'type'       => BaseArticleAttribute::ATTRIBUTE_PI,
                ])
                ->exists() || BaseCompanyFeatureTagRelation::find()
                ->where([
                    'company_id'     => $this->announcementInfo->company_id,
                    'feature_tag_id' => BaseCompanyFeatureTag::PI_TAG_ID,
                ])
                ->exists()) {
            $announcementExtraInfo->is_pi = BaseAnnouncementExtra::IS_PI_YES;
        } else {
            $announcementExtraInfo->is_pi = BaseAnnouncementExtra::IS_PI_NO;
        }
        if ($announcementExtraInfo->is_pay == BaseAnnouncementExtra::IS_PAY_NO || $announcementExtraInfo->is_boshihou_pay == BaseAnnouncementExtra::IS_PAY_NO) {
            BaseShowcase::updateCompanyStatIsPay([$this->announcementInfo->company_id]);
        }

        $announcementExtraInfo->save();
    }

    /**
     * 更新博士后公告表
     */
    protected function updateAnnouncementBoShiHouTable()
    {
        if (BaseAnnouncementBoshihou::find()
                ->where(['announcement_id' => $this->announcementId])
                ->exists() && !BaseJob::find()
                ->where([
                    'job_category_id' => self::BOSHIHOU_CATEGORY_ID,
                    'status'          => [
                        BaseJob::STATUS_ONLINE,
                        BaseJob::STATUS_OFFLINE,
                    ],
                    'is_show'         => BaseJob::IS_SHOW_YES,
                    'announcement_id' => $this->announcementId,
                ])
                ->exists()) {
            //删除
            BaseAnnouncementBoshihou::deleteAll(['announcement_id' => $this->announcementId]);
        }
        if (!BaseAnnouncementBoshihou::find()
                ->where(['announcement_id' => $this->announcementId])
                ->exists() && BaseJob::find()
                ->where([
                    'job_category_id' => self::BOSHIHOU_CATEGORY_ID,
                    'status'          => [
                        BaseJob::STATUS_ONLINE,
                        BaseJob::STATUS_OFFLINE,
                    ],
                    'is_show'         => BaseJob::IS_SHOW_YES,
                    'announcement_id' => $this->announcementId,
                ])
                ->exists()) {
            // 添加
            $model                  = new BaseAnnouncementBoshihou();
            $model->announcement_id = $this->announcementId;
            $model->save();
        }

        return true;
    }

    /**
     * 更新职位公告地区、学历中间表
     */
    protected function updateJobAnnouncementRelation()
    {
        //删除职位公告地区中间表数据
        BaseAnnouncementAreaRelation::deleteAll(['announcement_id' => $this->announcementId]);
        //删除职位公告学历中间表数据
        BaseAnnouncementEducationRelation::deleteAll(['announcement_id' => $this->announcementId]);
        //获取公告下所有职位(在线、下线)
        $jobList        = BaseJob::find()
            ->select([
                'province_id',
                'city_id',
                'education_type',
            ])
            ->where([
                'announcement_id' => $this->announcementId,
                'status'          => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->asArray()
            ->all();
        $provinceIds    = array_unique(array_column($jobList, 'province_id'));
        $cityIds        = array_unique(array_column($jobList, 'city_id'));
        $educationCodes = array_unique(array_column($jobList, 'education_type'));

        //批量写入表中
        $areaInsert = [];
        foreach ($provinceIds as $provinceId) {
            $item         = [
                'announcement_id' => $this->announcementId,
                'area_id'         => $provinceId,
                'level'           => 1,
            ];
            $areaInsert[] = $item;
        }
        foreach ($cityIds as $cityId) {
            $item         = [
                'announcement_id' => $this->announcementId,
                'area_id'         => $cityId,
                'level'           => 2,
            ];
            $areaInsert[] = $item;
        }
        if ($areaInsert) {
            BaseAnnouncementAreaRelation::getDb()
                ->createCommand()
                ->batchInsert(BaseAnnouncementAreaRelation::tableName(), [
                    'announcement_id',
                    'area_id',
                    'level',
                ], $areaInsert)
                ->execute();
        }

        //学历要求
        $educationInsert = [];
        foreach ($educationCodes as $educationCode) {
            $item              = [
                'announcement_id' => $this->announcementId,
                'education_code'  => $educationCode,
            ];
            $educationInsert[] = $item;
        }
        if ($educationInsert) {
            BaseAnnouncementEducationRelation::getDb()
                ->createCommand()
                ->batchInsert(BaseAnnouncementEducationRelation::tableName(), [
                    'announcement_id',
                    'education_code',
                ], $educationInsert)
                ->execute();
        }
    }

    /**
     * 更新单位扩展表里公告数量
     */
    protected function updateStatAnnouncementCount()
    {
        // 在线公告条件
        $onLineCount = BaseAnnouncement::find()
            ->where([
                'company_id' => $this->announcementInfo->company_id,
                'status'     => BaseAnnouncement::STATUS_ONLINE,
            ])
            ->count();

        $allCount = BaseAnnouncement::find()
            ->alias('a')
            ->leftJoin(['art' => BaseArticle::tableName()], 'a.article_id = art.id')
            ->where([
                'a.company_id' => $this->announcementInfo->company_id,
                'art.is_show'  => BaseArticle::IS_SHOW_YES,
                'a.status'     => [
                    BaseAnnouncement::STATUS_OFFLINE,
                    BaseAnnouncement::STATUS_ONLINE,
                ],
            ])
            ->count();

        $statModel                            = BaseCompanyStatData::findOne(['company_id' => $this->announcementInfo->company_id]);
        $statModel->all_announcement_count    = $allCount ?: 0;
        $statModel->online_announcement_count = $onLineCount ?: 0;
        $statModel->save();
    }

    /**
     * 验证套餐状态(基础类)
     */
    protected function baseCheckMemberPackage()
    {
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            // 企业自己操作的
            $this->companyPackageConfigModel = BaseCompanyPackageConfig::findOne([
                'company_id' => $this->companyInfo->id,
                'status'     => BaseCompanyPackageConfig::STATUS_ACTIVE,
            ]);
            if (!$this->companyPackageConfigModel) {
                BaseCompanyPackageConfig::setCurrencyCompanyPackageConfig($this->companyInfo->id);
                $this->companyPackageConfigModel = BaseCompanyPackageConfig::findOne([
                    'company_id' => $this->companyInfo->id,
                    'status'     => BaseCompanyPackageConfig::STATUS_ACTIVE,
                ]);
            }

            if (!$this->companyPackageConfigModel) {
                throw new \Exception('套餐不存在');
            }

            if ($this->companyPackageConfigModel->effect_time > CUR_DATETIME) {
                throw new \Exception('套餐还没生效');
            }
        }
    }

    /**
     * 检查是否可以发布
     */
    protected function checkRelease()
    {
        // 找到现在还有多少公告发布的条数
        $remainAmount = $this->companyPackageConfigModel->announcement_amount;

        if ($this->announcementInfo->is_consume_release == BaseAnnouncement::IS_CONSUME_RELEASE_NO) {
            if ($remainAmount <= 0) {
                throw new \Exception('公告发布数量已达上限');
            }
        }

        // 下面开始检查公告的发布间隔时间
        if ($this->articleInfo->status != BaseArticle::STATUS_ONLINE) {
            $releaseTime = $this->articleInfo->release_time;

            // 未曾发布成功过
            if ($releaseTime && $releaseTime != TimeHelper::ZERO_TIME) {
                $subDay = TimeHelper::computeDaySub($releaseTime, CUR_DATETIME);

                if ($subDay <= $this->companyPackageConfigModel->announcement_release_interval_day) {
                    throw new \Exception('公告发布间隔时间未到');
                }
            }
        }

        return true;
    }

    /**
     * 更新公告UUID
     * @return void
     * @throws \yii\base\Exception
     */
    protected function updateUuid()
    {
        $model       = BaseAnnouncement::findOne($this->announcementId);
        $model->uuid = UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT, $model->id);
        $model->save();
    }
}