<?php
/**
 * create user：shannon
 * create time：2025/5/15 上午11:08
 */
namespace common\service\v2\announcement;

class AfterService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 处理公告的一些后置操作
     */
    public function run($announcementId)
    {
        $this->announcementId = $announcementId;
        $this->updateJobAnnouncementAmount();
        $this->runAutoColumnAfter();
    }
}