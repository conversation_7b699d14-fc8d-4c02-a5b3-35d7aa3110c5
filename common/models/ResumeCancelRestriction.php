<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_cancel_restriction".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property string $mobile 手机号
 * @property string $mobile_code 手机号区号
 * @property string $email 邮箱
 * @property int $cancel_log_id 注销日志id
 * @property string $restriction_end_time 限制结束时间（注销时间+180天）
 */
class ResumeCancelRestriction extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_cancel_restriction';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'restriction_end_time'], 'safe'],
            [['cancel_log_id'], 'integer'],
            [['mobile', 'mobile_code'], 'string', 'max' => 16],
            [['email'], 'string', 'max' => 256],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'mobile' => 'Mobile',
            'mobile_code' => 'Mobile Code',
            'email' => 'Email',
            'cancel_log_id' => 'Cancel Log ID',
            'restriction_end_time' => 'Restriction End Time',
        ];
    }
}
