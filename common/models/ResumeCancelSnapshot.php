<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_cancel_snapshot".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $member_id 求职者会员id
 * @property int $resume_id 简历id
 * @property int $cancel_log_id 注销日志id
 * @property string $mobile 手机号快照
 * @property string $mobile_code 手机号区号快照
 * @property string $email 邮箱快照
 * @property string $username 用户名快照
 * @property string $name 姓名快照
 * @property string $resume_data_json 简历完整数据快照（JSON）
 */
class ResumeCancelSnapshot extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_cancel_snapshot';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['member_id', 'resume_id', 'cancel_log_id'], 'integer'],
            [['resume_data_json'], 'string'],
            [['mobile', 'mobile_code'], 'string', 'max' => 16],
            [['email'], 'string', 'max' => 256],
            [['username', 'name'], 'string', 'max' => 32],
            [['cancel_log_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'member_id' => 'Member ID',
            'resume_id' => 'Resume ID',
            'cancel_log_id' => 'Cancel Log ID',
            'mobile' => 'Mobile',
            'mobile_code' => 'Mobile Code',
            'email' => 'Email',
            'username' => 'Username',
            'name' => 'Name',
            'resume_data_json' => 'Resume Data Json',
        ];
    }
}
