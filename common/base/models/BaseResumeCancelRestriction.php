<?php

namespace common\base\models;

use common\models\ResumeCancelRestriction;

class BaseResumeCancelRestriction extends ResumeCancelRestriction
{
    // 限制状态常量
    const STATUS_ACTIVE  = 1;      // 生效中
    const STATUS_EXPIRED = 2;     // 已失效

    const STATUS_LIST = [
        self::STATUS_ACTIVE  => '生效中',
        self::STATUS_EXPIRED => '已失效',
    ];

    /**
     * 获取状态名称
     * @param int $status 状态
     * @return string
     */
    public static function getStatusName($status)
    {
        return self::STATUS_LIST[$status] ?? '未知';
    }
}
