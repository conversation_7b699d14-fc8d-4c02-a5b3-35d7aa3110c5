<?php

namespace common\base\models;

use common\models\ResumeCancelLog;

class BaseResumeCancelLog extends ResumeCancelLog
{
    // 注销原因类型常量
    const CANCEL_REASON_TYPE_FOUND_JOB        = 1;          // 已找到新工作，以后不再打算找工作
    const CANCEL_REASON_TYPE_CHANGE_MOBILE    = 2;          // 换手机号了，重新注册
    const CANCEL_REASON_TYPE_WRONG_ACCOUNT    = 3;          // 注册企业账号，误操作为个人账号
    const CANCEL_REASON_TYPE_MULTIPLE_ACCOUNT = 4;          // 已有多个账号，想注销一个
    const CANCEL_REASON_TYPE_PRIVACY_CONCERN  = 5;          // 担心隐私泄露
    const CANCEL_REASON_TYPE_NO_INVITATION    = 6;          // 不想接收到邀约邮件
    const CANCEL_REASON_TYPE_OTHER            = 99;          // 其他原因

    const CANCEL_REASON_TYPE_LIST = [
        self::CANCEL_REASON_TYPE_FOUND_JOB        => '已找到新工作，以后不再打算找工作',
        self::CANCEL_REASON_TYPE_CHANGE_MOBILE    => '换手机号了，重新注册',
        self::CANCEL_REASON_TYPE_WRONG_ACCOUNT    => '注册企业账号，误操作为个人账号',
        self::CANCEL_REASON_TYPE_MULTIPLE_ACCOUNT => '已有多个账号，想注销一个',
        self::CANCEL_REASON_TYPE_PRIVACY_CONCERN  => '担心隐私泄露',
        self::CANCEL_REASON_TYPE_NO_INVITATION    => '不想接收到邀约邮件',
        self::CANCEL_REASON_TYPE_OTHER            => '其他原因',
    ];

    // 注销申请状态常量
    const STATUS_APPLYING  = 1;    // 申请中
    const STATUS_WITHDRAWN = 2;   // 已撤回
    const STATUS_COMPLETED = 3;   // 已完成

    const STATUS_LIST = [
        self::STATUS_APPLYING  => '申请中',
        self::STATUS_WITHDRAWN => '已撤回',
        self::STATUS_COMPLETED => '已完成',
    ];

    // 短信发送状态常量
    const SMS_STATUS_WAIT          = 0;    // 未发送短信
    const SMS_STATUS_APPLY_SENT    = 1;    // 已发送申请成功短信
    const SMS_STATUS_REMINDER_SENT = 2;    // 已发送提醒短信（第6天）
    const SMS_STATUS_COMPLETE_SENT = 3;    // 已发送完成注销短信

    const SMS_STATUS_LIST = [
        self::SMS_STATUS_WAIT          => '未发送',
        self::SMS_STATUS_APPLY_SENT    => '已发送申请成功短信',
        self::SMS_STATUS_REMINDER_SENT => '已发送提醒短信',
        self::SMS_STATUS_COMPLETE_SENT => '已发送完成短信',
    ];

    /**
     * 获取注销原因类型名称
     * @param int $type 注销原因类型
     * @return string
     */
    public static function getCancelReasonTypeName($type)
    {
        return self::CANCEL_REASON_TYPE_LIST[$type] ?? '未知';
    }

    /**
     * 获取状态名称
     * @param int $status 状态
     * @return string
     */
    public static function getStatusName($status)
    {
        return self::STATUS_LIST[$status] ?? '未知';
    }

    /**
     * 根据简历ID获取最后一条简历日志
     * @param $resumeId
     */
    public static function getLastResumeLog($resumeId)
    {
        // 查询简历日志表，根据简历ID进行筛选，按ID降序排列，返回第一条记录
        return self::find()
            ->where(['resume_id' => $resumeId])
            ->orderBy('id DESC')
            ->asArray()
            ->one();
    }
}
