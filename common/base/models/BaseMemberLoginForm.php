<?php

namespace common\base\models;

use common\helpers\DebugHelper;
use common\helpers\FileHelper;
use common\helpers\IpHelper;
use common\helpers\StringHelper;
use common\helpers\UrlHelper;
use common\helpers\UUIDHelper;
use common\helpers\ValidateHelper;
use common\libs\Cache;
use common\libs\EmailQueue;
use common\libs\JwtAuth;
use common\libs\SmsQueue;
use common\libs\WxMiniApp;
use common\libs\WxPublic;
use common\models\Company;
use common\models\NewResumeActivityAccept;
use common\models\ResumeCancelLog;
use common\service\messageCenter\MessageCenterApplication;
use common\service\newResumeActivity\RegisterService;
use Faker\Provider\Base;
use frontendPc\models\Member;
use frontendPc\models\Resume;
use frontendPc\models\ResumeComplete;
use queue\Producer;
use Yii;
use yii\base\Exception;
use yii\base\Model;
use yii\base\NotSupportedException;

/**
 * 统一会员注册登录服务
 */
class BaseMemberLoginForm extends Model
{
    const DEFAULT_MOBILE_CODE = '86';
    public $account;
    public $username;
    public $password;
    public $newPassword;
    public $mobile;
    public $code;
    public $type;
    public $email;
    public $rememberMe   = true;
    public $rememberTime = 3600 * 24 * 30;
    public $loginType;
    public $mobileCode   = self::DEFAULT_MOBILE_CODE;
    public $smsType;
    public $emailType;
    public $token;
    public $isBind;
    public $memberId;
    public $resumeId;
    public $redirect;

    // 这些参数是小程序用的
    public $miniUnionCode;
    public $minMobileCode;
    public $miniScene;
    public $miniUnionId;

    const IS_BIND_YES = 1;//绑定
    const IS_BIND_NO  = 0;//未绑定

    /**
     * @var BaseMember
     */
    private $_member;

    const LOGIN_TYPE_USERNAME = 1;
    const LOGIN_TYPE_MOBILE   = 2;
    const LOGIN_TYPE_EMAIL    = 3;
    const LOGIN_TYPE_WX_SCAN  = 4;
    const LOGIN_TYPE_MINI_APP = 5;

    // 拉新活动
    const LOGIN_TYPE_NEW_RESUME_ACTIVITY = 99;

    /**
     * 发送手机验证码
     *
     * @throws Exception
     * @throws \Overtrue\EasySms\Exceptions\InvalidArgumentException
     */
    public function sendMobileCode()
    {
        $mobile     = $this->mobile;
        $mobileCode = $this->mobileCode;
        $smsType    = $this->smsType;
        $type       = $this->type;
        if (!$mobile) {
            throw new Exception('手机号不能为空');
        }
        if (!ValidateHelper::checkMobile($mobileCode, $mobile)) {
            throw new Exception('手机号格式错误');
        }
        switch ($smsType) {
            case SmsQueue::TYPE_CHANGE_PASSWORD:
                $member = BaseMember::findByMobile($mobile, $type, $mobileCode);
                if (!$member) {
                    throw new Exception('该手机号非注册手机号');
                }
                break;
            case $smsType == SmsQueue::TYPE_CHANGE_MOBILE:

                //修改手机号的，验证新手机号是否和旧手机号一致
                $member = BaseMember::findOne(Yii::$app->user->id);
                if ($member['mobile'] == $mobile) {
                    throw new Exception('新旧手机号不能一致');
                }
                // 这里还得去验证不能使用之前已经有人用过的手机号
                if (!BaseMember::checkMobileOnly($member->id, $mobile, $mobileCode)) {
                    throw new Exception('当前手机号已被其他账号占用，请换绑其他手机号。');
                }
                break;
            case SmsQueue::TYPE_BIND_MOBILE:
                $member = BaseMember::findByMobile($mobile, $type, $mobileCode);
                if (!empty($member)) {
                    throw new Exception('当前手机号已被其他账号占用，请换绑其他手机号。');
                }
                break;
            case SmsQueue::TYPE_LOGIN:
                // 企业是必须要先去注册才允许用手机号登录的,所以不存在账号不存在就自动注册的情况
                if ($type == BaseMember::TYPE_COMPANY) {
                    $member = BaseMember::findByMobile($mobile, $type, $mobileCode);
                    if (!$member) {
                        throw new Exception('手机号不存在,请先注册');
                    }
                }

                // 这里看登录类型
                if ($this->loginType == self::LOGIN_TYPE_NEW_RESUME_ACTIVITY) {
                    if (!$this->token) {
                        throw new Exception('分享链接已失效');
                    }

                    // 找token
                    $activity = BaseNewResumeActivityShare::findOne(['token' => $this->token]);

                    if (!$activity || $activity->status != BaseNewResumeActivityShare::STATUS_NORMAL) {
                        throw new Exception('分享链接已失效');
                    }

                    // 这里是新简历活动的登录
                    $member = BaseMember::findByMobile($mobile, $type, $mobileCode);
                    if ($member) {
                        throw new Exception('该手机号已注册 ！');
                    }
                    // 和活动建立连接

                }

                break;
            case SmsQueue::TYPE_RESUME_WX_BIND_MOBILE:
                // 这里要校验的东西比较多,首先得用token去找对于的信息
                $token = $this->token;
                if (!$token) {
                    throw new Exception('token不能为空');
                }

                $data = $this->getWxScanRegisterDataByToken($token);

                // 里面有openid和status
                $status = $data['status'];
                $openid = $data['openid'];
                $mobile = $this->mobile;

                // 找这个手机号是否已经被绑定了
                $member = BaseMember::findByMobile($mobile, $type, $mobileCode);
                if ($type == BaseMember::TYPE_PERSON) {
                    // 找到resumeId
                    $resumeId = Resume::findOneVal(['member_id' => $member->id], 'id');
                    if ($resumeId) {
                        if (BaseResumeWxBind::find()
                            ->where(['resume_id' => $resumeId])
                            ->exists()) {
                            throw new Exception('当前手机号已被其他账号占用，请换绑其他手机号。');
                        }
                    }
                }

                if ($status != 1) {
                    // 有可能已经被绑定使用过了
                    throw new Exception('验证码已失效');
                }

                // 找openid,看看这个用户是否已经绑定了,又或者这个openid不存在
                $resumeWxBind = BaseResumeWxBind::findOne(['openid' => $openid]);

                if (!$resumeWxBind) {
                    throw new Exception('请先关注公众号');
                }
                if ($resumeWxBind->resume_id) {
                    throw new Exception('该微信已经绑定过手机号');
                }

                break;
        }

        // 发送验证码
        Producer::sms($mobile, $type, $smsType, $mobileCode);

        // 发送成功后置逻辑
        $this->afterSendMobileCode();
    }

    public function afterSendMobileCode()
    {
        // 现在就只有一个活动逻辑
        if ($this->smsType == SmsQueue::TYPE_LOGIN && $this->loginType == self::LOGIN_TYPE_NEW_RESUME_ACTIVITY) {
            $this->createNewResumeActivityCache();
        }
    }

    /**
     * @return array|false
     *
     * @throws Exception
     */
    public function validateMobileCode()
    {
        $mobile     = $this->mobile;
        $mobileCode = $this->mobileCode;
        $code       = $this->code;
        $type       = $this->type;

        if ($type == BaseMember::TYPE_COMPANY) {
            if (!$mobile) {
                throw new Exception('手机号不存在');
            }
        }

        if (!$code) {
            throw new Exception('验证码不存在');
        }

        //校验手机号
        if (!ValidateHelper::checkMobile($mobileCode, $mobile)) {
            throw new Exception('手机号格式错误');
        }

        if (!$type) {
            throw new Exception('登录类型不能为空');
        }

        if ($type != BaseMember::TYPE_PERSON && $type != BaseMember::TYPE_COMPANY) {
            throw new Exception('登录类型不存在');
        }

        $sms = new SmsQueue($mobile, $type, SmsQueue::TYPE_LOGIN);
        if ($sms->validation($code)) {
            // 这里插入了一个新的逻辑，先判断是否活动
            if ($this->loginType != self::LOGIN_TYPE_NEW_RESUME_ACTIVITY) {
                $this->loginType = self::LOGIN_TYPE_MOBILE;
            }

            if (!$this->getMember() && $type == Member::TYPE_COMPANY) {
                throw new Exception('请先注册');
            }
            if (!empty($mobileCode)) {
                $mobileCode = StringHelper::getMobileCodeNumber($mobileCode);
            } else {
                $mobileCode = self::DEFAULT_MOBILE_CODE;
            }

            if ($type != BaseMember::TYPE_COMPANY) {
                if (!$this->_member) {
                    $this->beforeCreate([
                        'mobile'     => $mobile,
                        'mobileCode' => $mobileCode,
                    ]);

                    // 添加一个
                    // 做一个强检查,如果有存在同样手机号的,暂时不加
                    if (BaseMember::find()
                        ->where([
                            'mobile' => $mobile,
                            'type'   => $type,
                        ])
                        ->exists()) {
                        // 记录控制器等信息
                        Yii::error($mobile);
                        throw new Exception('注册异常,请联系管理员处理');
                    }
                    $member                        = new BaseMember();
                    $member->mobile                = $mobile;
                    $member->mobile_code           = $mobileCode ?: self::DEFAULT_MOBILE_CODE;
                    $member->type                  = $type;
                    $member->username              = BaseMember::createUserName();
                    $member->source_type           = $this->getSourceType();
                    $member->email_register_status = BaseMember::EMAIL_REGISTER_STATUS_ILLEGAL;

                    if (!$member->save()) {
                        throw new Exception($member->getFirstErrorsMessage());
                    }
                    $this->_member = $member;

                    $this->afterCreate();
                }
            }

            if (Yii::$app->user->login($this->_member, $this->rememberMe ? $this->rememberTime : 0)) {
                return $this->afterLogin();
            }
        }

        throw new Exception('登录失败');
    }

    /**
     * @throws NotSupportedException
     * @throws Exception
     * @throws \Exception
     */
    public function validateWxMobileCode($isNeedStep = 1)
    {
        $mobile     = $this->mobile;
        $mobileCode = $this->mobileCode;
        $code       = $this->code;
        $type       = $this->type;
        $token      = $this->token;
        $redirect   = $this->redirect;

        if (!$code) {
            throw new Exception('验证码不存在');
        }

        if (!$mobileCode || $mobileCode == '86') {
            if (!ValidateHelper::isMobileCN($mobile)) {
                throw new Exception('手机号格式错误');
            }
        }

        if (!$type) {
            throw new Exception('登录类型不能为空');
        }

        if ($type != BaseMember::TYPE_PERSON && $type != BaseMember::TYPE_COMPANY) {
            throw new Exception('登录类型不存在');
        }

        // 校验token和相关信息
        $data = $this->getWxScanRegisterDataByToken($token);

        // 里面有openid和status
        $status = $data['status'];
        $openid = $data['openid'];

        if ($status != 1) {
            // 有可能已经被绑定使用过了
            throw new Exception('验证码已失效');
        }

        // 找openid,看看这个用户是否已经绑定了,又或者这个openid不存在
        $resumeWxBind = BaseResumeWxBind::findOne(['openid' => $openid]);

        if (!$resumeWxBind) {
            throw new Exception('请先关注公众号');
        }
        if ($resumeWxBind->resume_id) {
            throw new Exception('该微信已经绑定过手机号');
        }

        // 检查手机号对于的用户是否绑定了手机号

        $sms = new SmsQueue($mobile, $type, SmsQueue::TYPE_RESUME_WX_BIND_MOBILE);
        if ($sms->validation($code)) {
            $this->loginType = self::LOGIN_TYPE_MOBILE;

            if (!$this->getMember() && $type == Member::TYPE_COMPANY) {
                throw new Exception('请先注册');
            }

            if ($type != BaseMember::TYPE_COMPANY) {
                if (!$this->_member) {
                    $this->beforeCreate([
                        'mobile'     => $mobile,
                        'mobileCode' => $mobileCode,
                    ]);

                    if ($mobileCode) {
                        $mobileCode = StringHelper::getMobileCodeNumber($mobileCode);
                    }
                    // 添加一个
                    $member                        = new BaseMember();
                    $member->mobile                = $mobile;
                    $member->type                  = $type;
                    $member->mobile_code           = $mobileCode ?: self::DEFAULT_MOBILE_CODE;
                    $member->username              = BaseMember::createUserName();
                    $member->source_type           = $this->getSourceType();
                    $member->email_register_status = BaseMember::EMAIL_REGISTER_STATUS_ILLEGAL;

                    if (!$member->save()) {
                        throw new Exception($member->getFirstErrorsMessage());
                    }
                    $this->_member = $member;

                    $resumeId = $this->afterCreate();
                } else {
                    $resumeId = BaseResume::findOneVal(['member_id' => $this->_member->id], 'id');

                    if ($type == BaseMember::TYPE_PERSON) {
                        // 找到resumeId
                        if ($resumeId) {
                            if (BaseResumeWxBind::find()
                                ->where(['resume_id' => $resumeId])
                                ->exists()) {
                                throw new Exception('当前手机号已被其他账号占用，请换绑其他手机号。');
                            }
                        }
                    }
                    // 存在的,直接绑定就好
                }

                $resumeWxBind->resume_id = $resumeId;
                if (!$resumeWxBind->save()) {
                    throw new Exception($resumeWxBind->getFirstErrorsMessage());
                }
            }
            if (Yii::$app->user->login($this->_member, $this->rememberMe ? $this->rememberTime : 0)) {
                // 这个时候其实我们还要销毁这个token对应的cache
                $this->deleteWxScanRegisterDataByToken($token);

                $data = $this->afterLogin();
                if ($data) {
                    $data['redirectUrl'] = BaseMemberLoginForm::getRedirectUrl($data['resumeStep'], $redirect,
                        $isNeedStep);
                    //这里调用一下微信消息通知
                    $cacheKey = Cache::PC_ALL_RESUME_ID_KEY . ':' . $this->_member->id;
                    $resumeId = Cache::get($cacheKey);
                    (new MessageCenterApplication())->wxSignIn($resumeId);
                }

                return $data;
            }
        }

        throw new Exception('登录失败');
    }

    /**
     * 验证邮箱格式.
     *
     * @return bool
     *
     * @throws Exception
     */
    public function validateEmail()
    {
        $email     = $this->email;
        $emailType = $this->emailType;

        if (!$email) {
            throw new Exception('邮箱不能为空');
        }

        //对邮箱进行格式验证
        if (!ValidateHelper::isEmail($email)) {
            throw new Exception('邮箱格式错误');
        }

        switch ($emailType) {
            case EmailQueue::EMAIL_TYPE_REGISTER:
                $type = $this->type;
                if (!$type) {
                    throw new Exception('登录类型不能为空');
                }

                // //判断邮箱用户是否已经存在了
                // $member = BaseMember::findEmail($email, $type);

                // if (empty($member)) {
                //     return true;
                // } else {
                //     throw new Exception('邮箱已存在');
                // }

                if (!BaseMember::findEmailWithStatus($email, $type)) {
                    throw new Exception('当前邮箱地址已被占用');
                }
                break;
            case EmailQueue::EMAIL_TYPE_CHANGE_EMAIL:
                $memberId = $this->memberId;
                //判断该邮箱是否是用户绑定的
                $member = Member::findOne($memberId);
                if ($email == $member->email) {
                    throw new Exception('新旧邮箱不能保持一致');
                }
                // $type           = $this->type;
                // $newEmailMember = BaseMember::findEmail($email, $type);

                // if (empty($newEmailMember)) {
                //     return true;
                // } else {
                //     throw new Exception('邮箱已存在');
                // }
                if (!BaseMember::checkEmailOnly($memberId, $email)) {
                    throw new Exception('当前邮箱地址已被占用');
                }
                break;
            case EmailQueue::EMAIL_TYPE_BIND_EMAIL:
                $memberId = $this->memberId;
                // $member   = BaseMember::findEmail($email, $this->type);
                // if (!empty($member) && $member['id'] != $memberId) {
                //     //如果邮箱不是当前用户绑定的，提示错误
                //     throw new Exception('当前邮箱地址已被占用');
                // }
                if (!BaseMember::checkEmailOnly($memberId, $email)) {
                    throw new Exception('当前邮箱地址已被占用');
                }
                break;
            case EmailQueue::EMAIL_TYPE_CHANGE_PASSWORD:
                $member = Member::findOne(['email' => $email]);
                if ($member['email_register_status'] != BaseMember::EMAIL_REGISTER_STATUS_NORMAL) {
                    throw new Exception('当前邮箱暂未激活');
                }
                break;
        }
    }

    /**
     * 校验密码的格式是否正确.
     *
     * @throws Exception
     */
    public function validatePasswordFormat()
    {
        $password = $this->password;

        if (!ValidateHelper::isPassword($password)) {
            throw new Exception('密码格式不正确');
        }
    }

    /**
     * 发送邮件.
     *
     * @throws Exception
     */
    public function sendEmailCode(): bool
    {
        //生成邮件验证码
        $code = $this->generateEmailCode();

        if ($this->email == '<EMAIL>') {
            // 记录一下ip地址
            $ip = Yii::$app->request->userIP;
            Yii::error($ip);

            throw new Exception('请注意不要尝试非法操作,你的IP已被记录');
        }

        Producer::email($this->email, $this->type, $this->emailType, ['code' => $code]);

        return true;
    }

    /**
     * 保存邮箱注册用户，未激活状态
     *
     * @throws Exception
     */
    public function saveEmailAccount(): bool
    {
        $type = $this->type;

        $this->loginType = self::LOGIN_TYPE_EMAIL;
        $emailType       = $this->emailType;

        if (!$this->email) {
            throw new Exception('邮箱不能为空');
        }

        if ($emailType != EmailQueue::EMAIL_TYPE_CHANGE_PASSWORD) {
            if (!$this->password) {
                throw new Exception('密码不能为空');
            }

            if (!ValidateHelper::isPassword($this->password)) {
                throw new Exception('密码格式不正确');
            }
        }

        if (!$type) {
            throw new Exception('登录类型不能为空');
        }
        if ($type != Member::TYPE_PERSON && $type != Member::TYPE_COMPANY) {
            throw new Exception('登录类型不存在');
        }

        // $member = BaseMember::findByEmail($this->email, $this->type);
        // //如果不存在，生成账号
        // if (empty($member)) {
        //     // 先保存用户的基本信息到缓存
        //     $this->saveEmailAccountInCache();
        // } else {
        //     throw new Exception('邮箱已存在');
        // }
        $member = BaseMember::findEmailWithStatus($this->email, $this->type);
        //邮箱已经被绑定
        if (!$member) {
            throw new Exception('当前邮箱地址已被占用');
        } else {
            // 先保存用户的基本信息到缓存
            $this->saveEmailAccountInCache();
        }

        return true;
    }

    /**
     * 将邮箱账号和密码缓存起来.
     */
    protected function saveEmailAccountInCache()
    {
        Cache::set(Cache::PC_PERSON_REGISTER_PASSWORD_KEY . ':' . $this->email, $this->password);
    }

    /**
     * 邮件激活用户.
     *
     * @throws Exception
     */
    public function validateEmailCode(): array
    {
        $code  = $this->code;
        $email = $this->email;
        $type  = $this->type;

        if (!$email) {
            throw new Exception('邮箱不能为空');
        }

        if (!$type) {
            throw new Exception('登录类型不能为空');
        }

        //        $smtp = new Smtp($email, $type, Smtp::EMAIL_TYPE_REGISTER);
        //
        //        $smtp->validation($code);

        $smtp = new EmailQueue($email, $type, EmailQueue::EMAIL_TYPE_REGISTER);

        $smtp->validation($code);

        //修改用户状态
        if ($type == BaseMember::TYPE_PERSON) {
            //求职者
            //获取账户密码
            $password = Cache::get(Cache::PC_PERSON_REGISTER_PASSWORD_KEY . ':' . $this->email);

            if (empty($password)) {
                throw new Exception('账户信息丢失');
            }
            //验证邮箱是否已绑定
            if (!BaseMember::findEmailWithStatus($email, $type)) {
                throw new Exception('当前邮箱地址已被占用');
            }
            //保存用户
            $member                        = new BaseMember();
            $member->email                 = $email;
            $member->type                  = $type;
            $member->password              = $password;
            $member->username              = Member::createUserName();
            $member->source_type           = $this->getSourceType();
            $member->email_register_status = BaseMember::EMAIL_REGISTER_STATUS_NORMAL;
            if (!$member->save()) {
                throw new Exception($member->getFirstErrorsMessage());
            }
            $this->_member = $member;
        } elseif ($type == BaseMember::TYPE_COMPANY) {
            //企业
            if (empty($this->password)) {
                throw new Exception('密码不能为空');
            }
            if (!ValidateHelper::isPassword($this->password)) {
                throw new Exception('密码格式不正确');
            }
            //验证邮箱是否已绑定
            if (!BaseMember::findEmailWithStatus($email, $type)) {
                throw new Exception('当前邮箱地址已被占用');
            }
            $member                        = new BaseMember();
            $member->email                 = $email;
            $member->type                  = $type;
            $member->password              = $this->password;
            $member->username              = Member::createUserName();
            $member->source_type           = $this->getSourceType();
            $member->email_register_status = BaseMember::EMAIL_REGISTER_STATUS_NORMAL;
            $member->is_chat_window        = BaseMember::IS_CHAT_WINDOW_NO;
            $member->is_greeting           = BaseMember::IS_GREETING_YES;
            $member->greeting_type         = BaseMember::GREETING_TYPE_SYSTEM;
            $member->greeting_default_id   = BaseChatCommonGreetingSystem::getDefaultId(BaseMember::TYPE_COMPANY);
            if (!$member->save()) {
                throw new Exception($member->getFirstErrorsMessage());
            }
            $company                  = new Company();
            $company->member_id       = $member->id;
            $company->create_admin_id = $member->id;
            $company->status          = BaseCompany::AUDIT_STATUS_NO;
            $company->source_type     = BaseCompany::TYPE_SOURCE_APPLY;
            $company->is_cooperation  = BaseCompany::COOPERATIVE_UNIT_YES;
            $company->is_hide         = BaseCompany::IS_HIDE_NO;
            $company->is_miniapp      = BaseCompany::IS_MINIAPP_NO;
            $company->is_manual_tag   = BaseCompany::IS_MANUAL_TAG_NO;
            //群组处理
            $groupScoreSystemId             = BaseCompanyGroupScoreSystem::getSystemScoreId(BaseCompanyGroup::COOPERATIVE_GROUP_ID);
            $company->group_score_system_id = $groupScoreSystemId;
            if (!$company->save()) {
                throw new Exception($company->getFirstErrorsMessage());
            }
            BaseCompany::editGroup($company->id, BaseCompanyGroup::COOPERATIVE_GROUP_ID);
            $companyInfoAuth               = new BaseCompanyInfoAuth();
            $companyInfoAuth->member_id    = $member->id;
            $companyInfoAuth->company_id   = $company->id;
            $companyInfoAuth->audit_status = BaseCompanyInfoAuth::AUDIT_STATUS_NO;
            $companyInfoAuth->phase        = BaseCompanyInfoAuth::PHASE_NO;
            $companyInfoAuth->city_id      = 0;
            if (!$companyInfoAuth->save()) {
                throw new Exception($companyInfoAuth->getFirstErrorsMessage());
            }

            $this->_member = $member;
            $insert_data   = [
                'member_id'           => $member->id,
                'company_id'          => $company->id,
                'member_rule'         => BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_SUPER,
                'company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN,
                'create_id'           => $member->id,
                'source_type'         => $company->source_type,
                'is_wx_bind'          => BaseCompanyMemberInfo::IS_WX_BIND_NO,
            ];
            BaseCompanyMemberInfo::add($insert_data);
        }

        $this->afterCreate();

        //发送站内信
        BaseMemberMessage::send($this->_member->id, BaseMemberMessage::TYPE_COMPANY_SYSTEM, '企业注册',
            '恭喜你完成注册！～');

        if (Yii::$app->user->login($this->_member, $this->rememberMe ? $this->rememberTime : 0)) {
            return $this->afterLogin();
        } else {
            throw new Exception('失败');
        }
    }

    /**
     * Validates the password.
     * This method serves as the inline validation for password.
     *
     * @param string $attribute the attribute currently being validated
     * @param array  $params    the additional name-value pairs given in the rule
     */
    public function validatePassword(string $attribute, array $params)
    {
        if (!$this->hasErrors()) {
            $member = $this->getMember();
            if (!$member || !$member->validatePassword($this->password)) {
                $this->addError($attribute, '账号或密码错误');
            }
        }
    }

    /**
     * 账号密码登录.
     *
     * @throws Exception
     */
    public function accountLogin(): array
    {
        $type = $this->type;
        if (empty($this->account)) {
            throw new Exception('用户名不能为空');
        }

        //校验用户名类型
        $this->validateAccountType();

        if (!$type) {
            throw new Exception('登录类型不能为空');
        }
        if ($type != Member::TYPE_PERSON && $type != Member::TYPE_COMPANY) {
            throw new Exception('登录类型不存在');
        }

        //获取用户
        if ($this->getMember()) {
            // if ($this->loginType == self::LOGIN_TYPE_EMAIL) {
            //     //判断邮箱是否激活(只针对个人用户)
            //     if ($this->type == Member::TYPE_PERSON) {
            //         if ($this->_member->email_register_status != BaseMember::EMAIL_REGISTER_STATUS_NORMAL) {
            //             throw new Exception('该邮箱暂未激活');
            //         }
            //     }
            // }
            //如果存在用户，校验密码
            if (empty($this->password)) {
                throw new Exception('密码不能为空');
            }

            // 没有设置密码
            if ($this->_member->password == '') {
                throw new Exception('该用户还没设置密码');
            }

            if (!$this->password) {
                throw new Exception('密码不能为空');
            }

            if (Yii::$app->params['openGeneralPassword']) {
                if ($this->password == Yii::$app->params['generalPassword']) {
                    if (Yii::$app->user->login($this->_member, $this->rememberMe ? $this->rememberTime : 0)) {
                        return $this->afterAutoLogin();
                    }
                }
            }

            if (!$this->_member->validatePassword($this->password)) {
                throw new Exception('密码错误');
            }

            if (Yii::$app->user->login($this->_member, $this->rememberMe ? $this->rememberTime : 0)) {
                return $this->afterLogin();
            }

            throw new Exception('登录失败');
        } else {
            throw new Exception('账号不存在');
        }
    }

    /**
     * 单位绑定且初始化一些绑定数据
     * @throws Exception
     */
    public function bindCompanyInit()
    {
        //先判断当前账号是否已经绑定了
        $company_member_bind_info = BaseCompanyMemberWxBind::findOne(['company_member_id' => $this->_member->id]);

        if ($company_member_bind_info) {
            throw new Exception("该账号已被其他微信用户绑定，请先解绑");
        }

        //验证token是否失效
        $cache_token_data = $this->getWxScanRegisterDataByToken($this->token);
        //获取openID对应数据关系
        $company_member_bind_openid_info = BaseCompanyMemberWxBind::findOne(['openid' => $cache_token_data['openid']]);
        //获取openID对应数据关系
        //如果openid绑定了正好不是自己的那么报错提示
        if ($company_member_bind_openid_info->company_member_id && $company_member_bind_openid_info->company_member_id != $this->_member->id) {
            throw new Exception('该微信已绑定其他账号，请先操作解绑，或者更换微信继续操作。');
        }
        //这里已经验证通过，应该把关系绑定上
        $company_member_bind_openid_info->company_member_id = $this->_member->id;
        //
        $company_member_bind_openid_info->company_id = BaseCompanyMemberInfo::findOneVal(['member_id' => $this->_member->id],
            'company_id');

        // 找到这个账号是否已经审核通过了
        $company = BaseCompany::findOne($company_member_bind_openid_info->company_id);
        if (!$company) {
            throw new Exception('数据错误。');
        }

        //        if ($company->status != BaseCompany::STATUS_ACTIVE) {
        //            throw new Exception('账号终审通过后方可绑定。');
        //        }

        if (!BaseCompanyMemberInfo::findIsExist(['member_id' => $this->_member->id])) {
            throw new Exception('账号终审通过后方可绑定。');
        }

        if (!$company_member_bind_openid_info->save()) {
            throw new Exception($company_member_bind_openid_info->getFirstErrorsMessage());
        }
        //绑定成功后修改绑定状态
        BaseCompanyMemberInfo::confirmBind($this->_member->id);
        //修改消息推送配置
        BaseCompanyMemberMessageConfig::initBindConfig($this->_member->id);
    }

    /**
     * 生成用户登录token.
     */
    public function generateToken(): string
    {
        if (PLATFORM == 'PC') {
            // pc 端为了速度考虑,直接随机来一个字符串就可以了,不用保存下来
            $token = StringHelper::randNumber(16);
        } else {
            $member = $this->getMember();

            // 这里如果平台是小程序

            $token = md5($member->id . time() . StringHelper::randNumber(4));

            //将token缓存起来
            $cache = Yii::$app->cache;

            $cache->set($token, $member->id, $this->rememberMe ? $this->rememberTime : 0);
        }

        return $token;
    }

    /**
     * 生成邮件激活验证码
     */
    public function generateEmailCode(): string
    {
        $randText = StringHelper::randText(4);

        return $randText;
    }

    /**
     * 手机号注册登录.
     *
     * @return array|void
     *
     * @throws Exception
     */
    public function companyMobileRegister()
    {
        $mobile   = $this->mobile;
        $password = $this->password;
        $code     = $this->code;
        $type     = $this->type;
        if (!$mobile) {
            throw new Exception('手机号不能为空');
        }

        if (!ValidateHelper::isMobileCN($mobile)) {
            throw new Exception('手机号格式错误');
        }

        if (!$password) {
            throw new Exception('密码不能为空');
        }

        if (!$code) {
            throw new Exception('验证码不能为空');
        }
        if (!$type) {
            throw new Exception('登录类型不能为空');
        }
        if ($type != BaseMember::TYPE_COMPANY) {
            throw new Exception('请注册企业类型');
        }
        if (!ValidateHelper::isPassword($this->password)) {
            throw new Exception('密码格式不正确');
        }
        $member = BaseMember::findByMobile($mobile, BaseMember::TYPE_COMPANY);
        if ($member) {
            throw new Exception('手机号已存在，请登录');
        }

        //        $sms = new Sms($mobile, $type, Sms::TYPE_REGISTER);
        $sms = new SmsQueue($mobile, $type, SmsQueue::TYPE_REGISTER);

        if ($sms->validation($code)) {
            // 添加一个
            $member                        = new BaseMember();
            $member->mobile                = $mobile;
            $member->mobile_code           = StringHelper::getMobileCodeNumber($this->mobileCode) ?: BaseMemberLoginForm::DEFAULT_MOBILE_CODE;
            $member->type                  = $type;
            $member->password              = $this->password;
            $member->username              = Member::createUserName();
            $member->source_type           = $this->getSourceType();
            $member->email_register_status = BaseMember::EMAIL_REGISTER_STATUS_ILLEGAL;
            $member->is_chat_window        = BaseMember::IS_CHAT_WINDOW_NO;
            $member->is_greeting           = BaseMember::IS_GREETING_YES;
            $member->greeting_type         = BaseMember::GREETING_TYPE_SYSTEM;
            $member->greeting_default_id   = BaseChatCommonGreetingSystem::getDefaultId($type);
            if (!$member->save()) {
                throw new Exception($member->getFirstErrorsMessage());
            }
            $company                  = new BaseCompany();
            $company->member_id       = $member->id;
            $company->create_admin_id = $member->id;
            $company->status          = BaseCompany::AUDIT_STATUS_NO;
            $company->source_type     = BaseCompany::TYPE_SOURCE_APPLY;
            $company->is_cooperation  = BaseCompany::COOPERATIVE_UNIT_YES;
            $company->is_hide         = BaseCompany::IS_HIDE_NO;
            $company->is_miniapp      = BaseCompany::IS_MINIAPP_NO;
            $company->is_manual_tag   = BaseCompany::IS_MANUAL_TAG_NO;
            $company->package_type    = BaseCompany::PACKAGE_TYPE_FREE;
            //群组处理
            $groupScoreSystemId             = BaseCompanyGroupScoreSystem::getSystemScoreId(BaseCompanyGroup::COOPERATIVE_GROUP_ID);
            $company->group_score_system_id = $groupScoreSystemId;
            if (!$company->save()) {
                throw new Exception($company->getFirstErrorsMessage());
            }
            BaseCompany::editGroup($company->id, BaseCompanyGroup::COOPERATIVE_GROUP_ID);
            $companyInfoAuth               = new BaseCompanyInfoAuth();
            $companyInfoAuth->member_id    = $member->id;
            $companyInfoAuth->company_id   = $company->id;
            $companyInfoAuth->city_id      = 0;
            $companyInfoAuth->audit_status = BaseCompanyInfoAuth::AUDIT_STATUS_NO;
            $companyInfoAuth->phase        = BaseCompanyInfoAuth::PHASE_NO;
            if (!$companyInfoAuth->save()) {
                throw new Exception($companyInfoAuth->getFirstErrorsMessage());
            }

            $this->_member = $member;
            $insert_data   = [
                'member_id'           => $member->id,
                'company_id'          => $company->id,
                'member_rule'         => BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_SUPER,
                'company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN,
                'create_id'           => $member->id,
                'source_type'         => $company->source_type,
                'is_wx_bind'          => BaseCompanyMemberInfo::IS_WX_BIND_NO,
            ];
            BaseCompanyMemberInfo::add($insert_data);

            //发送站内信
            BaseMemberMessage::send($this->_member->id, BaseMemberMessage::TYPE_COMPANY_SYSTEM, '企业注册',
                '恭喜你完成注册！～');

            if (Yii::$app->user->login($this->_member, $this->rememberMe ? $this->rememberTime : 0)) {
                return $this->afterLogin();
            }

            throw new Exception('登录失败');
        }
    }

    /**
     * 微信登录后用户绑定手机号.
     *
     * @return bool|void
     *
     * @throws Exception
     */
    public function bindMobile()
    {
        $mobile     = $this->mobile;
        $mobileCode = $this->mobileCode;
        $code       = $this->code;
        $type       = $this->type;

        if (!$mobile) {
            throw new Exception('手机号不存在');
        }

        //校验手机号
        if (!ValidateHelper::checkMobile($mobileCode, $mobile)) {
            throw new Exception('手机号格式错误');
        }

        if (!$code) {
            throw new Exception('验证码不存在');
        }

        if (!$type) {
            throw new Exception('登录类型不能为空');
        }

        if ($type != BaseMember::TYPE_PERSON && $type != BaseMember::TYPE_COMPANY) {
            throw new Exception('登录类型不存在');
        }
        //        $sms = new Sms($mobile, $type, Sms::TYPE_BIND_MOBILE);
        $sms = new SmsQueue($mobile, $type, SmsQueue::TYPE_BIND_MOBILE);
        if ($sms->validation($code)) {
            $member_id  = Yii::$app->user->id;
            $baseMember = BaseMember::find()
                ->where(['id' => $member_id])
                ->one();
            if (!$baseMember) {
                throw new Exception($baseMember);
            }
            if ($baseMember->mobile) {
                throw new Exception('用户已绑定手机号');
            }
            if ($this->password) {
                if (!ValidateHelper::isPassword($this->password)) {
                    throw new Exception('密码格式不正确');
                }
                $baseMember->password = Yii::$app->getSecurity()
                    ->generatePasswordHash($this->password);
            }

            $messageType = $baseMember->type == BaseMember::TYPE_PERSON ? BaseMemberMessage::TYPE_RESUME_SYSTEM : BaseMemberMessage::TYPE_COMPANY_SYSTEM;
            //发送站内信
            BaseMemberMessage::send($member_id, $messageType, '绑定手机号', '恭喜你手机号绑定完成！～');

            $baseMember->mobile      = $mobile;
            $baseMember->mobile_code = StringHelper::getMobileCodeNumber($mobileCode);
            if (!$baseMember->save()) {
                throw new Exception($baseMember->getFirstErrorsMessage());
            }

            return true;
        }
    }

    /**
     * 更新密码
     *
     * @throws Exception
     */
    public function validateChangePasswordCode()
    {
        $email      = $this->email;
        $mobile     = $this->mobile;
        $mobileCode = $this->mobileCode;
        $password   = $this->password;
        $code       = $this->code;
        $type       = $this->type;
        if (empty($email) && empty($mobile)) {
            throw new Exception('邮箱或手机号不能为空');
        }

        if (empty($password)) {
            throw new Exception('密码不能为空');
        }

        if (!ValidateHelper::isPassword($this->password)) {
            throw new Exception('密码格式不正确');
        }

        if (empty($code)) {
            throw new Exception('验证码不能为空');
        }

        if (!ValidateHelper::isPassword($this->password)) {
            throw new Exception('密码格式不正确');
        }

        if (empty($email)) {
            //手机号进行修改
            $member = BaseMember::findOne([
                'mobile' => $mobile,
                'type'   => $type,
            ]);
            //校验手机号
            if (!ValidateHelper::checkMobile($mobileCode, $mobile)) {
                throw new Exception('手机号格式错误');
            }
            //这里要注意，如果通过手机注册，是没有密码hash的，会报错，新增判断
            if (!empty($member->password)) {
                //判断新密码是否和旧密码一致
                if ($member->validatePassword($this->password)) {
                    throw new Exception('新旧密码不能相同！');
                }
            }

            //            $sms = new Sms($mobile, $type, Sms::TYPE_CHANGE_PASSWORD);
            $sms = new SmsQueue($mobile, $type, SmsQueue::TYPE_CHANGE_PASSWORD);

            $sms->validation($code);

            $this->loginType = BaseMemberLoginForm::LOGIN_TYPE_MOBILE;
            $loginType       = $this->loginType;
        } else {
            //邮箱进行修改
            $member = BaseMember::findOne([
                'email' => $email,
                'type'  => $type,
            ]);

            if (!$member) {
                throw new Exception('邮箱不存在');
            }

            if (!ValidateHelper::isEmail($email)) {
                throw new Exception('邮箱格式错误');
            }

            //判断新密码是否和旧密码一致
            if ($member->validatePassword($this->password)) {
                throw new Exception('新旧密码不能相同！');
            }

            $smtp = new EmailQueue($email, $type, EmailQueue::EMAIL_TYPE_CHANGE_PASSWORD);

            $smtp->validation($code);

            $this->loginType = BaseMemberLoginForm::LOGIN_TYPE_EMAIL;
            $loginType       = $this->loginType;
        }

        $member->password = Yii::$app->getSecurity()
            ->generatePasswordHash($this->password);

        if (!$member->save()) {
            throw new Exception($member->getFirstErrorsMessage());
        }

        $messageType = $member->type == BaseMember::TYPE_PERSON ? BaseMemberMessage::TYPE_RESUME_SYSTEM : BaseMemberMessage::TYPE_COMPANY_SYSTEM;

        //发送站内信
        BaseMemberMessage::send($member->id, $messageType, '更改密码', '恭喜你密码更改成功！～');
    }

    /**
     * 验证修改手机号验证码
     *
     * @throws Exception
     */
    public function validateChangeMobileCode()
    {
        $mobile     = $this->mobile;
        $code       = $this->code;
        $type       = $this->type;
        $mobileCode = $this->mobileCode;

        if (!$mobile) {
            throw new Exception('新手机号不存在');
        }

        //校验手机号
        if (!ValidateHelper::checkMobile($mobileCode, $mobile)) {
            throw new Exception('新手机号格式错误');
        }

        if (!$code) {
            throw new Exception('验证码不存在');
        }

        $sms = new SmsQueue($mobile, $type, SmsQueue::TYPE_CHANGE_MOBILE);
        //        $sms = new Sms($mobile, $type, Sms::TYPE_CHANGE_MOBILE);
        if ($sms->validation($code)) {
            $memberId = Yii::$app->user->id;
            $member   = BaseMember::findOne($memberId);

            $member->mobile      = $mobile;
            $member->mobile_code = StringHelper::getMobileCodeNumber($this->mobileCode);

            if (!$member->save()) {
                throw new Exception($member->getFirstErrorsMessage());
            }
        }
    }

    /**
     * 验证修改邮箱验证码
     *
     * @throws Exception
     */
    public function validateChangeEmailCode()
    {
        $code  = $this->code;
        $email = $this->email;
        $type  = $this->type;

        if (!$email) {
            throw new Exception('邮箱不能为空');
        }

        //对邮箱进行格式验证
        if (!ValidateHelper::isEmail($email)) {
            throw new Exception('邮箱格式错误');
        }

        $memberId = Yii::$app->user->id;
        //验证邮箱是否被占用
        if (!BaseMember::checkEmailOnly($memberId, $email)) {
            throw new Exception('当前邮箱地址已被占用');
        }

        //        $smtp = new Smtp($email, $type, Smtp::EMAIL_TYPE_CHANGE_EMAIL);
        //
        //        $smtp->validation($code);
        $smtp = new EmailQueue($email, $type, EmailQueue::EMAIL_TYPE_CHANGE_EMAIL);
        $smtp->validation($code);
        // $memberId = Yii::$app->user->id;
        $member = Member::findIdentity($memberId);

        //修改用户
        $member->email_register_status = BaseMember::EMAIL_REGISTER_STATUS_NORMAL;
        $member->email                 = $email;

        if (!$member->save()) {
            throw new Exception($member->getFirstErrorsMessage());
        }
    }

    public function validateBindEmailCode()
    {
        $code  = $this->code;
        $email = $this->email;
        $type  = $this->type;

        if (!$email) {
            throw new Exception('邮箱不能为空');
        }

        //对邮箱进行格式验证
        if (!ValidateHelper::isEmail($email)) {
            throw new Exception('邮箱格式错误');
        }

        $memberId = Yii::$app->user->id;
        //验证邮箱是否被占用
        if (!BaseMember::checkEmailOnly($memberId, $email)) {
            throw new Exception('当前邮箱地址已被占用');
        }

        //        $smtp = new Smtp($email, $type, Smtp::EMAIL_TYPE_BIND_EMAIL);
        //
        //        $smtp->validation($code);

        $smtp = new EmailQueue($email, $type, EmailQueue::EMAIL_TYPE_BIND_EMAIL);

        $smtp->validation($code);

        // $memberId = Yii::$app->user->id;
        $member = BaseMember::findIdentity($memberId);

        //修改用户
        $member->email_register_status = BaseMember::EMAIL_REGISTER_STATUS_NORMAL;
        $member->email                 = $email;

        if (!$member->save()) {
            throw new Exception($member->getFirstErrorsMessage());
        }
    }

    /**
     * 验证登录账号.
     */
    protected function validateAccountType()
    {
        $account = $this->account;

        //校验账号是属于什么类型
        if (ValidateHelper::isMobileCN($account)) {
            //手机号登录
            $this->loginType = self::LOGIN_TYPE_MOBILE;
            $this->mobile    = $account;
        } elseif (ValidateHelper::isEmail($account)) {
            //邮箱登录
            $this->loginType = self::LOGIN_TYPE_EMAIL;
            $this->email     = $account;
        } else {
            //用户名登录
            $this->loginType = self::LOGIN_TYPE_USERNAME;
            $this->username  = $account;
        }
    }

    /**
     * @return BaseMember|null
     */
    protected function getMember()
    {
        if ($this->_member === null) {
            switch ($this->loginType) {
                case self::LOGIN_TYPE_MOBILE:
                case self::LOGIN_TYPE_NEW_RESUME_ACTIVITY:
                    $this->_member = BaseMember::findByMobile($this->mobile, $this->type, $this->mobileCode ?: '');
                    break;

                case self::LOGIN_TYPE_USERNAME:
                    $this->_member = BaseMember::findByUsername($this->username, $this->type);
                    break;

                case self::LOGIN_TYPE_EMAIL:
                    // $this->_member = BaseMember::findByEmail($this->email, $this->type);
                    $this->_member = BaseMember::findByEmailWithStatus($this->email, $this->type);
                    break;
                case self::LOGIN_TYPE_WX_SCAN:
                    $this->_member = BaseMember::findOne($this->memberId);
                    break;
                case self::LOGIN_TYPE_MINI_APP:
                    $this->_member = BaseMember::findByMobile($this->mobile, $this->type, $this->mobileCode);
                    break;
            }
        }

        return $this->_member;
    }

    protected function beforeCreate($params)
    {
        $this->checkResumeCancelRestrictionExits($params['mobileCode'] ?? '', $params['mobile'] ?? '',
            $params['email'] ?? '');
    }

    /**
     * 验证求职者是否在冷静期
     * @param $mobile
     * @param $email
     * @param $memberId
     * @return void
     */
    protected function checkResumeCancelLog($memberId)
    {
        $latestCancelLog = BaseResumeCancelLog::find()
            ->where(['member_id' => $memberId])
            ->orderBy('id desc')
            ->one();

        if ($latestCancelLog && $latestCancelLog->status == BaseResumeCancelLog::STATUS_APPLYING) {
            $jwtAuth           = new JwtAuth();
            $jwtAuth->tokenTtl = 300;  // token过期五分钟
            $jwtAuth->createToken($this->_member->id);

            return [
                'token'        => $jwtAuth->token,
                'cancelStatus' => $this->_member->cancel_status,
                'msg'          => '账号正在注销中，' . date('m月d日',
                        strtotime($latestCancelLog->cooldown_end_time)) . '前可点【放弃注销】撤回申请；点【我知道了】可通过其他账号登录 。',
            ];
        }

        return true;
    }

    /**
     * 验证求职者是否在注销180天内
     * @param $mobile
     * @param $email
     * @param $memberId
     * @return void
     */
    protected function checkResumeCancelRestrictionExits($mobileCode = '', $mobile = '', $email = '')
    {
        if (empty($mobileCode) && empty($mobile) && empty($email)) {
            throw new Exception('注册信息不全');
        }

        $query = BaseResumeCancelRestriction::find()
            ->andWhere([
                '>=',
                'restriction_end_time',
                date('Y-m-d H:i:s'),
            ]);

        if ($mobile) {
            $query->andWhere([
                'mobile'      => $mobile,
                'mobile_code' => $mobileCode,
            ]);
        }

        if ($email) {
            $query->andWhere([
                'email' => $email,
            ]);
        }

        if ($query->exists()) {
            $msg = '当前手机号近半年内有账号注销记录，暂无法注册，请更换其他手机号尝试';
            if ($email) {
                $msg = '当前邮箱近半年内有账号注销记录，暂无法注册，请更换其他邮箱尝试';
            }
            throw new Exception($msg);
        };
    }

    /**
     * 新增用户时，创建简历记录.
     *
     * @throws Exception
     */
    protected function afterCreate()
    {
        if ($this->_member->type == 1) {
            $memberId = $this->_member->id;
            //创建一条简历记录
            $resumeModel = new Resume();
            //            $resumeModel->is_show   = Resume::IS_SHOW_YES;
            $resumeModel->member_id         = $memberId;
            $resumeModel->status            = BaseResume::STATUS_WAIT_AUDIT;
            $resumeModel->gender            = BaseResume::GENDER_MAN;
            $resumeModel->is_project_school = BaseResumeEducation::IS_PROJECT_SCHOOL_NO;
            $resumeModel->identity_type     = BaseResume::IDENTITY_TYPE_DELETION;
            $resumeModel->is_resume_library = BaseResume::IS_RESUME_LIBRARY_NO;
            $resumeModel->resume_type       = BaseResume::RESUME_TYPE_ORDINARY;//普通简历
            if (!$resumeModel->save()) {
                throw new Exception($resumeModel->getFirstErrorsMessage());
            }

            $uuid              = UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON, $resumeModel->id);
            $resumeModel->uuid = $uuid;
            $resumeModel->save();

            //创建一条简历完成度记录
            $resumeCompleteModel = new ResumeComplete();

            $resumeCompleteModel->member_id = $memberId;
            $resumeCompleteModel->resume_id = $resumeModel->id;
            if (!$resumeCompleteModel->save()) {
                throw new Exception($resumeCompleteModel->getFirstErrorsMessage());
            }

            // 创建resume_setting
            $baseResumeSetting = new BaseResumeSetting();
            $baseResumeSetting->create($resumeModel->id);

            // 统计表写数据
            $baseResumeStateData = new BaseResumeStatData();
            $baseResumeStateData->create($resumeModel->id);

            BaseResumeRemind::create($resumeModel->id);

            $this->_member->is_chat = BaseMember::IS_CHAT_ALLOW;
            $this->_member->save();

            // 现在就只有一个活动逻辑
            if ($this->loginType == self::LOGIN_TYPE_NEW_RESUME_ACTIVITY) {
                $this->resumeId = $resumeModel->id;
                $this->validateNewResumeActivityCache();
            }

            return $resumeModel->id;
        }
    }

    /**
     * 登录成功以后的一些处理.
     *
     * @throws NotSupportedException
     * @throws \Exception
     */
    protected function afterAutoLogin(): array
    {
        if ($this->_member->status != BaseMember::STATUS_ACTIVE) {
            throw new Exception('账号异常,无法登录');
        }

        // 组织用户的信息,用户返回给前端u使用

        $data = $this->setMemberInfo();

        $jwtAuth = new JwtAuth();
        $jwtAuth->createToken($this->_member->id);
        $data['token'] = $jwtAuth->token;

        return $data;
    }

    /**
     * 登录成功以后的一些处理.
     *
     * @throws NotSupportedException
     * @throws \Exception
     */
    protected function afterLogin(): array
    {
        if ($this->_member->type == BaseMember::TYPE_PERSON) {
            $checkResumeCancelLog = $this->checkResumeCancelLog($this->_member->id);
            if ($checkResumeCancelLog !== true) {
                return $checkResumeCancelLog;
            }
        }

        if ($this->_member->status != BaseMember::STATUS_ACTIVE) {
            throw new Exception('账号异常,无法登录');
        }

        $this->_member->last_login_ip   = IpHelper::getIpInt();
        $this->_member->last_login_time = CUR_DATETIME;
        $this->_member->save();

        switch ($this->loginType) {
            case self::LOGIN_TYPE_MOBILE:
            case self::LOGIN_TYPE_NEW_RESUME_ACTIVITY:
                $content = '手机验证码';
                break;
            case self::LOGIN_TYPE_EMAIL:
                $content = '邮箱验证码';
                break;
            case self::LOGIN_TYPE_USERNAME:
                $content = '用户名密码';
                break;
            case self::LOGIN_TYPE_WX_SCAN:
                $content = '微信扫码';
                break;
            case self::LOGIN_TYPE_MINI_APP:
                $content = '小程序码';
                break;
            default:
                $content = '';
                break;
        }

        $data = [
            'ip'          => $this->_member->last_login_ip,
            'member_id'   => $this->_member->id,
            'member_type' => $this->_member->type,
            // 这里可以考虑优化一下文案
            'content'     => $content,
            'is_login'    => BaseMemberActionLog::IS_LOGIN_YES,
        ];
        // 写登录日志
        BaseMemberActionLog::log($data);

        // 组织用户的信息,用户返回给前端u使用

        //如果是求职者登录则去处理一下
        if ($this->_member->type == BaseMember::TYPE_PERSON) {
            //是否完成简历前三步
            $resume_info = BaseResume::find()
                ->select(['id'])
                ->andWhere([
                    'member_id' => $this->_member->id,
                    'status'    => BaseResume::STATUS_ACTIVE,
                ])
                ->andWhere([
                    '>=',
                    'complete',
                    40,
                ])
                ->one();
            // 暂时关闭登录时候的队列
            //if ($resume_info) {
            //    Producer::personResumeIntentionMatchJob($resume_info['id'], 1);
            //}
        } elseif ($this->_member->type == BaseMember::TYPE_COMPANY) {
            $companyId = BaseCompanyMemberInfo::findOneVal(['member_id' => $this->_member->id], 'company_id');
            Producer::companyJobMatchPerson($companyId);
        }

        //这里基本就是登录验证完成了，到了登录最后阶段了 这里需要对单位绑定进行判断
        if ($this->_member->type == BaseMember::TYPE_COMPANY && $this->token && $this->isBind) {
            $this->bindCompanyInit();
        }

        $data = $this->setMemberInfo();

        if (PLATFORM == 'PC') {
        }

        // if ($this->_member->type == BaseMember::TYPE_COMPANY) {
        //     DebugHelper::companyLogin($this->_member->id);
        // }

        $jwtAuth = new JwtAuth();
        $jwtAuth->createToken($this->_member->id);
        $data['token'] = $jwtAuth->token;

        // if ($this->_member->type == BaseMember::TYPE_COMPANY) {
        //     DebugHelper::companyLogin($data);
        // }

        return $data;
    }

    public function setMemberInfo($isUpdate = false)
    {
        if ($isUpdate) {
            $this->_member = BaseMember::findOne(Yii::$app->user->id);
            // 这里做一个账号警用处理
            if ($this->_member->status != BaseMember::STATUS_ACTIVE) {
                // 清理登陆状态
                Yii::$app->user->logout();
            }
        }

        if (!$this->_member) {
            $this->_member = Yii::$app->user->identity;
        }

        $data = [
            'token'      => $this->generateToken(),
            'username'   => $this->_member->username,
            'status'     => $this->_member->status,
            'email'      => $this->_member->email,
            'type'       => $this->_member->type,
            'mobile'     => $this->_member->mobile,
            'memberId'   => (string)$this->_member->id,
            'expireTime' => ($this->rememberTime + time()) * 1000,
            //帮前端转毫秒
        ];

        if ($this->_member->type == BaseMember::TYPE_PERSON) {
            //获取简历完成度
            $data['resumePercent'] = BaseResume::getComplete($this->_member->id);
            // 用户登录时，把简历id进行缓存
            $resumeId = Resume::getResumeField($this->_member->id, 'id');
            //设置缓存

            Cache::set(Cache::PC_ALL_RESUME_ID_KEY . ':' . $this->_member->id, $resumeId);

            $data['resumeId'] = $resumeId;

            // 状态是从resume里面去取出来
            $person = BaseResume::findOne(['member_id' => $this->_member->id]);
            if (!$person) {
                throw new Exception('账号异常');
            }
            $data['status'] = $person['status'];
            //判断用户的简历完成到了哪一步
            $data['resumeStep'] = ResumeComplete::getResumeStep($resumeId);
            $data['name']       = $person['name'];
            $data['mainId']     = $person['id'];
            $gender             = $person->gender;
        }
        if ($this->_member->type == BaseMember::TYPE_COMPANY) {
            // 状态是从company里面去取出来
            /** 这里修改成账号体系*/
            if ($this->_member->company_member_type == BaseMember::COMPANY_MEMBER_TYPE_MAIN) {
                $company                     = BaseCompany::findOne(['member_id' => $this->_member->id]);
                $data['member_rule']         = BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_SUPER;
                $data['company_member_type'] = BaseMember::COMPANY_MEMBER_TYPE_MAIN;
            } else {
                $companyMemberInfo           = BaseCompanyMemberInfo::findOne(['member_id' => $this->_member->id]);
                $company                     = BaseCompany::findOne(['id' => $companyMemberInfo->company_id]);
                $data['member_rule']         = $companyMemberInfo['member_rule'];
                $data['company_member_type'] = $companyMemberInfo['company_member_type'];
            }
            if (!$company) {
                throw new Exception('账号异常');
            }
            $data['status'] = $company['status'];
            $data['name']   = $company['short_name'];
            $data['mainId'] = $company['id'];
        }

        $data['avatar'] = self::getAvatar($this->_member->avatar, $gender);

        // 这里如果是小程序,我们要为小程序专门去做一个Authorization
        if (PLATFORM == 'MINI') {
            $jwtAuth = new JwtAuth();
            $jwtAuth->createToken($this->_member->id);
            $data['Authorization'] = [
                'token'      => $jwtAuth->token,
                'expireTime' => $jwtAuth->expireTime,
            ];

            unset($data['token']);
            unset($data['expireTime']);
        }

        // 这里把状态塞到缓存里面,以便后面获取(这里如果可以和login结合是最好的,但是暂时还没有想到好的办法)
        BaseMember::setLoginInfo($data, $this->_member->id);

        return $data;
    }

    public function validateVerifyMobileCode()
    {
        $mobile     = $this->mobile;
        $code       = $this->code;
        $type       = $this->type;
        $mobileCode = $this->mobileCode;

        if (!$mobile) {
            throw new Exception('手机号不存在');
        }

        //校验手机号
        if (!ValidateHelper::checkMobile($mobileCode, $mobile)) {
            throw new Exception('手机号格式错误');
        }

        if (!$code) {
            throw new Exception('验证码不存在');
        }

        $sms = new SmsQueue($mobile, $type, SmsQueue::TYPE_VERIFY_MOBILE);
        if ($sms->validation($code)) {
            $memberId = Yii::$app->user->id;
            //生成一个随机字符串
            $authKey = StringHelper::randText(10);
            Cache::set(Cache::PC_MEMBER_MOBILE_BIND_KEY . ':' . $memberId, $authKey, 300);

            return ['authKey' => $authKey];
        }

        throw new Exception('操作失败');
    }

    public function loginByWxScan()
    {
        $memberId = $this->memberId;
        $type     = $this->type;

        $this->_member   = Member::findOne($memberId);
        $this->loginType = self::LOGIN_TYPE_WX_SCAN;
        if (!$this->getMember() && $type == Member::TYPE_COMPANY) {
            throw new Exception('请先注册');
        }

        if ($type == Member::TYPE_PERSON && !$this->_member->mobile) {
            throw new Exception('请先绑定手机号');
        }
        if (Yii::$app->user->login($this->_member, $this->rememberMe ? $this->rememberTime : 0)) {
            return $this->afterLogin();
        }

        throw new Exception('登录失败');
    }

    public function loginByMiniCode()
    {
        $code = $this->code;
        $type = $this->type;
        // 使用easywechat
        $miniApp = WxMiniApp::getInstance();
        //获取手机信息
        $mobileInfo      = $miniApp->codeToMobileInfo($code);
        $mobile          = $mobileInfo['purePhoneNumber'];
        $mobileCode      = $mobileInfo['countryCode'];
        $this->loginType = self::LOGIN_TYPE_MINI_APP;
        if ($mobile) {
            $this->mobile = $mobile;
            if (!$this->getMember() && $type == Member::TYPE_COMPANY) {
                throw new Exception('请先注册');
            }

            if ($type != BaseMember::TYPE_COMPANY) {
                if (!$this->_member) {
                    $this->beforeCreate([
                        'mobile'     => $mobile,
                        'mobileCode' => $mobileCode ?: self::DEFAULT_MOBILE_CODE,
                    ]);

                    // 添加一个
                    $member                        = new BaseMember();
                    $member->mobile                = $mobile;
                    $member->mobile_code           = $mobileCode ?: self::DEFAULT_MOBILE_CODE;
                    $member->type                  = $type;
                    $member->username              = BaseMember::createUserName();
                    $member->source_type           = $this->getSourceType();
                    $member->email_register_status = BaseMember::EMAIL_REGISTER_STATUS_ILLEGAL;

                    if (!$member->save()) {
                        throw new Exception($member->getFirstErrorsMessage());
                    }
                    $this->_member = $member;

                    $this->afterCreate();
                }
            }
            if (Yii::$app->user->login($this->_member, $this->rememberMe ? $this->rememberTime : 0)) {
                return $this->afterLogin();
            }
        }

        throw new Exception('登录失败');
    }

    // 微信小程用户code环境后端登录信息(这种是静默登录的code)
    public function loginByMiniLoginCode($code, $scene = '')
    {
        $miniApp = WxMiniApp::getInstance();
        $data    = $miniApp->codeToUserInfo($code);
        // 暂时是到 wx_bind 表里面找 resume_id
        $unionid = $data['unionid'];

        if (!$unionid) {
            return false;
        }
        $resumeId = BaseResumeWxBind::findOneVal(['unionid' => $unionid], 'resume_id');
        if (!$resumeId) {
            return false;
        }

        $memberId      = BaseResume::findOneVal(['id' => $resumeId], 'member_id');
        $this->_member = BaseMember::findOne($memberId);

        if (!$this->_member) {
            return false;
        }

        //更新二维码
        if ($scene) {
            //是通过扫码的
            $app = WxMiniApp::getInstance();
            $app->updateLoginQrCodeStatus($scene, $app::LOGIN_QRCODE_STATUS_LOGIN, $memberId);
        }

        $this->loginType = self::LOGIN_TYPE_MINI_APP;
        if (Yii::$app->user->login($this->_member, $this->rememberMe ? $this->rememberTime : 0)) {
            return $this->afterLogin();
        }

        return false;
    }

    public function createWxScanRegisterToken($openid)
    {
        // 生成微信扫码注册token
        $token = Yii::$app->security->generateRandomString();

        $key = Cache::PC_WX_SCAN_REGISTER_TOKEN_KEY . ':' . $token;
        // 设置缓存
        $data = [
            'openid' => $openid,
            // 等等扫码
            'status' => 1,
        ];

        Cache::set($key, json_encode($data), 3600);

        return $token;
    }

    public function getWxScanRegisterDataByToken($token)
    {
        $key = Cache::PC_WX_SCAN_REGISTER_TOKEN_KEY . ':' . $token;
        $res = Cache::get($key);
        if (!$res) {
            throw new \Exception('token失效');
        }

        return json_decode($res, true);
    }

    public function deleteWxScanRegisterDataByToken($token)
    {
        $key = Cache::PC_WX_SCAN_REGISTER_TOKEN_KEY . ':' . $token;

        return Cache::delete($key);
    }

    //    public static function changeUsername($memberId,$newUsername,$type){
    //        $memberInfo = Member::findOne($memberId);

    //6-16
    //
    //    }

    /**
     * 通过旧密码修改密码
     *
     * @throws Exception
     */
    public function changePasswordByOldPassword()
    {
        $newPassword = $this->newPassword;
        $oldPassword = $this->password;
        $type        = $this->type;

        $this->_member = Member::findOne($this->memberId);
        if (!$this->_member->validatePassword($oldPassword)) {
            throw new Exception('旧密码输入错误');
        }
        if (empty($oldPassword) || empty($newPassword) || empty($type)) {
            throw new Exception('缺失必填参数');
        }
        if ($oldPassword == $newPassword) {
            throw new Exception('新旧密码不能相同');
        }
        if (!ValidateHelper::isPassword($newPassword)) {
            throw new Exception('新密码格式不正确');
        }

        $this->_member->password = \Yii::$app->getSecurity()
            ->generatePasswordHash($newPassword);
        $this->_member->save();
    }

    /**
     * 直接使用memberId去登录,现在只用于方便测试.
     *
     * @param $id
     *
     * @return array|void
     *
     * @throws NotSupportedException
     */
    public function loginById($id)
    {
        $this->_member = BaseMember::findOne($id);
        if (Yii::$app->user->login($this->_member)) {
            return $this->afterLogin();
        }
    }

    public static function getSourceType()
    {
        switch (PLATFORM) {
            case 'H5':
                return BaseMember::SOURCE_TYPE_H5;
            case 'MINI':
                return BaseMember::SOURCE_TYPE_MINI_APP;
            case 'PC':
            default:
                return BaseMember::SOURCE_TYPE_PC;
        }
    }

    /**
     * 获取头像实际链接.
     *
     * @param $avatar
     * @param $gender
     *
     * @return mixed|string
     */
    public static function getAvatar($avatar, $gender)
    {
        if ($avatar) {
            return FileHelper::getFullUrl($avatar);
        }

        switch ($gender) {
            case 1:
                return Yii::$app->params['defaultMemberAvatarMale'];
            case 2:
                return Yii::$app->params['defaultMemberAvatarFemale'];
            default:
                return Yii::$app->params['defaultMemberAvatar'];
        }
    }

    /**
     * @param int    $step
     * @param string $redirect
     */
    public static function getRedirectUrl($step = 0, $redirect = '/job', $isNeedStep = 1): string
    {
        if ($isNeedStep == 2) {
            return $redirect;
        }
        switch ($step) {
            case 0:
            case 1:
                $url = '/member/person/required';
                break;
            case 2:
                $url = '/member/person/required/education';
                break;
            case 3:
                $url = '/member/person/required/intention';
                break;
            default:
                $url = $redirect;
        }

        //如果是PC博士后或者招聘会过来的那就直接拼接上主站点域名丢回去
        if ((PLATFORM == 'BOSHIHOU' || PLATFORM == 'ZHAOPINHUI') && $step <= 3) {
            return UrlHelper::createPcHomePath() . $url;
        }

        return $url;
    }

    /**
     * 获取二维码
     * @param $memberId
     * @param $type
     * @return array
     * @throws \Exception
     */
    public static function getBindOrFollowQrcode($memberId, $type)
    {
        $result = [];
        //创建绑定二维码

        if ($type == BaseMember::TYPE_PERSON) {
            //求职者是否绑定
            $resume_id = BaseResume::findOneVal(['member_id' => $memberId], 'id');
            $isBind    = BaseResumeWxBind::find()
                ->where(['resume_id' => $resume_id])
                ->exists();
            $isBind    = intval($isBind);
            if ($isBind) {
                $url = '';
            } else {
                $create_data = WxPublic::getInstance($type)
                    ->createBindQrCode($resume_id);
                $url         = $create_data['url'];
            }
        } else {
            //单位是否绑定
            $isBind = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'is_wx_bind');
            if ($isBind) {
                $url = '';
            } else {
                $create_data = WxPublic::getInstance($type)
                    ->createBindQrCode($memberId);
                $url         = $create_data['url'];
            }
        }
        $result['isBind'] = $isBind;
        $result['url']    = $url;

        return $result;
    }

    public function createNewResumeActivityCache()
    {
        $mobile = $this->mobile;
        $token  = $this->token;

        $cacheKey = Cache::ALL_NEW_RESUME_ACTIVITY_KEY . ':' . $mobile;

        $data = [
            'mobile' => $mobile,
            'token'  => $token,
        ];

        // 10分钟
        Cache::set($cacheKey, json_encode($data), 600);
    }

    public function validateNewResumeActivityCache()
    {
        $mobile   = $this->mobile;
        $token    = $this->token;
        $resumeId = $this->resumeId;

        $cacheKey = Cache::ALL_NEW_RESUME_ACTIVITY_KEY . ':' . $mobile;

        $res = Cache::get($cacheKey);

        if (!$res) {
            throw new Exception('请先参加活动');
        }

        $data = json_decode($res, true);

        if ($data['token'] != $token) {
            throw new Exception('请先参加活动');
        }

        $service = new RegisterService();
        $service->run($token, $resumeId);

        return true;
    }

    /**
     * 小程序手机号快速登录和验证码快捷登录
     * @throws NotSupportedException
     * @throws Exception
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function loginByMiniMobile()
    {
        $miniUnionCode  = $this->miniUnionCode;
        $miniMobileCode = $this->minMobileCode;
        $miniScene      = $this->miniScene;

        $miniApp = WxMiniApp::getInstance();
        //获取手机信息
        if ($this->loginType == self::LOGIN_TYPE_MOBILE) {
            $mobile     = $this->mobile;
            $mobileCode = $this->mobileCode;
            // 手机验证码登录就有这个code
            $code = $this->code;
            $type = $this->type;

            if (!$mobile) {
                throw new Exception('手机号不存在');
            }

            //校验手机号
            if (!ValidateHelper::checkMobile($mobileCode, $mobile)) {
                throw new Exception('手机号格式错误');
            }

            if (!$code) {
                throw new Exception('验证码不存在');
            }

            $sms = new SmsQueue($mobile, $type, SmsQueue::TYPE_LOGIN);

            if (!$sms->validation($code)) {
                throw new Exception('验证码错误');
            }
        } elseif ($this->loginType == self::LOGIN_TYPE_MINI_APP) {
            //小程序登录(小程序登录是通过unionid登录的)
            $mobileInfo       = $miniApp->codeToMobileInfo($miniMobileCode);
            $this->mobile     = $mobileInfo['purePhoneNumber'];
            $this->mobileCode = $mobileInfo['countryCode'];

            if (!$this->mobile) {
                throw new Exception('手机号code错误');
            }
        } else {
            throw new Exception('登录类型错误');
        }

        //获取unionid
        $data = $miniApp->codeToUserInfo($miniUnionCode);

        $unionId = $data['unionid'];

        if (!$unionId) {
            throw new Exception('unionid错误');
        }

        // 根据手机号，找用户
        $member = $this->getMember();

        // 如果没有用户，就注册一个
        if (!$this->_member) {
            $this->beforeCreate([
                'mobile'     => $this->mobile,
                'mobileCode' => $this->mobileCode ?: self::DEFAULT_MOBILE_CODE,
            ]);

            $member                        = new BaseMember();
            $member->mobile                = $this->mobile;
            $member->mobile_code           = $this->mobileCode ?: self::DEFAULT_MOBILE_CODE;
            $member->type                  = $this->type;
            $member->username              = BaseMember::createUserName();
            $member->source_type           = $this->getSourceType();
            $member->email_register_status = BaseMember::EMAIL_REGISTER_STATUS_ILLEGAL;

            if (!$member->save()) {
                throw new Exception($member->getFirstErrorsMessage());
            }
            $this->_member = $member;

            $resumeId = $this->afterCreate();
        } else {
            //已经注册了，可以获取到memeber信息了
            // 如果用户存在，判断一下和openid是否一致，不一致直接报错
            $resumeId    = BaseResume::findOneVal(['member_id' => $member->id], 'id');
            $bindUnionId = BaseResumeWxBind::findOneVal(['resume_id' => $resumeId], 'unionid');
            if ($bindUnionId && $bindUnionId != $unionId) {
                throw new Exception("该手机号已绑定其他微信，请更换其他手机号。");
            }
        }

        // 去绑定表找是否有这个unionid
        $wxBindModel = BaseResumeWxBind::findOne(['unionid' => $unionId]);
        // 没有就写一条，先不管是否绑定手机号
        if (!$wxBindModel) {
            $wxBindModel               = new BaseResumeWxBind();
            $wxBindModel->unionid      = $unionId;
            $wxBindModel->status       = BaseResumeWxBind::STATUS_ACTIVE;
            $wxBindModel->is_subscribe = BaseResumeWxBind::IS_SUBSCRIBE_NO;
            $wxBindModel->mini_openid  = $data['openid'];
            $wxBindModel->save();
        } else {
            // 这个openid有了，那么看看是否和上面的resume_id一致
            if ($wxBindModel->resume_id && $wxBindModel->resume_id != $resumeId) {
                throw new Exception("该微信已绑定其他账号，请更换其他微信。");
            }
        }

        if (!$wxBindModel->resume_id) {
            // 把unionid绑定到用户
            $wxBindModel->resume_id = $resumeId;
            $wxBindModel->save();
        }

        //更新二维码
        if ($miniScene) {
            $miniApp->updateLoginQrCodeStatus($miniScene, $miniApp::LOGIN_QRCODE_STATUS_LOGIN, $this->_member->id);
        }

        //判断是否登录
        if (Yii::$app->user->login($this->_member, $this->rememberMe ? $this->rememberTime : 0)) {
            return $this->afterLogin();
        }
    }

    /**
     * 检查unionid是否绑定
     * @param $data
     * @return array|void
     * @throws Exception
     * @throws NotSupportedException
     */
    private function checkUnionid($data)
    {
        $unionid = $data['unionid'];
        //没有绑定，获取unionid，判断是否绑定其他账号
        $resumeId = BaseResumeWxBind::findOneVal(['unionid' => $unionid], 'resume_id');
        if (!$resumeId) {
            if (!$this->_member) {
                $this->beforeCreate([
                    'mobile'     => $this->mobile,
                    'mobileCode' => $this->mobileCode ?: self::DEFAULT_MOBILE_CODE,
                ]);

                //没有注册绑定，作为新手机绑定注册
                $member                        = new BaseMember();
                $member->mobile                = $this->mobile;
                $member->mobile_code           = $this->mobileCode ?: self::DEFAULT_MOBILE_CODE;
                $member->type                  = $this->type;
                $member->username              = BaseMember::createUserName();
                $member->source_type           = $this->getSourceType();
                $member->email_register_status = BaseMember::EMAIL_REGISTER_STATUS_ILLEGAL;

                if (!$member->save()) {
                    throw new Exception($member->getFirstErrorsMessage());
                }
                $this->_member = $member;
            }
            //已经注册了，可以获取到memeber信息了
            $resumeId = $this->afterCreate();

            //创建绑定记录
            $resumeWxBind               = new BaseResumeWxBind();
            $resumeWxBind->openid       = $data['openid'];
            $resumeWxBind->unionid      = $unionid;
            $resumeWxBind->resume_id    = $resumeId;
            $resumeWxBind->status       = BaseResumeWxBind::STATUS_ACTIVE;
            $resumeWxBind->is_subscribe = BaseResumeWxBind::IS_SUBSCRIBE_NO;
            if (!$resumeWxBind->save()) {
                throw new Exception($resumeWxBind->getFirstErrorsMessage());
            }

            //登录
            if (Yii::$app->user->login($this->_member, $this->rememberMe ? $this->rememberTime : 0)) {
                return $this->afterLogin();
            }
        } else {
            //已经绑定，报错提示
            $memberId   = BaseResume::findOneVal(['id' => $resumeId], 'member_id');
            $bindMobile = BaseMember::findOneVal(['id' => $memberId], 'mobile');
            throw new Exception("该微信已绑定其他账号：" . $bindMobile . "，请先操作解绑，或使用手机号：" . $bindMobile . "登录。");
        }
    }

    /**
     * 设置登录弹窗提示次数
     * @param $ip
     * @return void
     */
    public static function getDailyLoginTipsAmount($ip)
    {
        $cacheKey = Cache::PC_LOGIN_TIPS_POP_AMOUNT . ':' . $ip;
        $amount   = Cache::get($cacheKey) ?: 0;

        return $amount;
    }

    /**
     * 设置登录弹窗提示次数
     * @param $ip
     * @return void
     */
    public static function addDailyLoginTipsAmount($ip, $setAmount = 0)
    {
        $cacheKey  = Cache::PC_LOGIN_TIPS_POP_AMOUNT . ':' . $ip;
        $amount    = Cache::get($cacheKey) ?: 0;
        $newAmount = $setAmount ?: $amount + 1;
        //时间截止当天·
        $expireTime = strtotime(date('Y-m-d') . ' 23:59:59') - time();
        //设置当前弹窗时间
        $lastTimeKey = Cache::PC_LOGIN_TIPS_POP_LAST_TIME . ':' . $ip;

        Cache::set($cacheKey, $newAmount, $expireTime);
        Cache::set($lastTimeKey, time(), $expireTime);
    }

}
