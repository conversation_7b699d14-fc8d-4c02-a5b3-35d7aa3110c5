<?php

/**
 * 手动验证脚本：验证求职者注销服务的驼峰格式字段
 * 运行方式：php tests/manual/ResumeCancelCamelCaseValidation.php
 */

require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/../../common/config/bootstrap.php';

use common\service\memberCancel\ResumeCancelSearchService;

echo "=== 求职者注销服务驼峰格式字段验证 ===\n\n";

// 测试1：验证getList方法返回的字段格式
echo "1. 测试 getList 方法返回字段格式...\n";
try {
    $params = [
        'page' => 1,
        'pageSize' => 5
    ];
    
    $result = ResumeCancelSearchService::getList($params);
    
    if (!empty($result['list'])) {
        $firstItem = $result['list'][0];
        
        // 检查驼峰格式字段
        $expectedCamelFields = [
            'statusText',
            'cancelReasonTypeText', 
            'smsStatusText',
            'applyTimeFormat',
            'cooldownEndTimeFormat',
            'completeTimeFormat',
            'withdrawTimeFormat',
            'mobileMasked',
            'emailMasked',
            'remainingCooldownDays',
            'operationType',
            'operationTypeText',
            'resumeSettingParsed'
        ];
        
        $missingFields = [];
        $wrongFormatFields = [];
        
        foreach ($expectedCamelFields as $field) {
            if (!array_key_exists($field, $firstItem)) {
                $missingFields[] = $field;
            }
        }
        
        // 检查是否还有下划线格式的字段
        $underscoreFields = [
            'status_text',
            'cancel_reason_type_text',
            'sms_status_text',
            'apply_time_format',
            'cooldown_end_time_format',
            'complete_time_format',
            'withdraw_time_format',
            'mobile_masked',
            'email_masked',
            'remaining_cooldown_days',
            'operation_type',
            'operation_type_text',
            'resume_setting_parsed'
        ];
        
        foreach ($underscoreFields as $field) {
            if (array_key_exists($field, $firstItem)) {
                $wrongFormatFields[] = $field;
            }
        }
        
        if (empty($missingFields) && empty($wrongFormatFields)) {
            echo "✅ getList 方法字段格式正确\n";
        } else {
            if (!empty($missingFields)) {
                echo "❌ 缺少驼峰格式字段: " . implode(', ', $missingFields) . "\n";
            }
            if (!empty($wrongFormatFields)) {
                echo "❌ 仍存在下划线格式字段: " . implode(', ', $wrongFormatFields) . "\n";
            }
        }
        
        // 显示实际的字段列表
        echo "实际返回的字段: " . implode(', ', array_keys($firstItem)) . "\n";
        
    } else {
        echo "⚠️  没有数据可供验证\n";
    }
    
} catch (Exception $e) {
    echo "❌ getList 方法测试失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 测试2：验证getStatistics方法返回的字段格式
echo "2. 测试 getStatistics 方法返回字段格式...\n";
try {
    $result = ResumeCancelSearchService::getStatistics();
    
    // 检查overview字段
    $expectedOverviewFields = ['totalApplies', 'todayApplies', 'weekApplies', 'monthApplies'];
    $overviewMissing = [];
    
    foreach ($expectedOverviewFields as $field) {
        if (!array_key_exists($field, $result['overview'])) {
            $overviewMissing[] = $field;
        }
    }
    
    // 检查统计数组的字段格式
    $statusStatsValid = true;
    if (!empty($result['statusStats'])) {
        $firstStat = $result['statusStats'][0];
        $expectedStatFields = ['status', 'statusName', 'count', 'percentage'];
        foreach ($expectedStatFields as $field) {
            if (!array_key_exists($field, $firstStat)) {
                $statusStatsValid = false;
                break;
            }
        }
    }
    
    if (empty($overviewMissing) && $statusStatsValid) {
        echo "✅ getStatistics 方法字段格式正确\n";
    } else {
        if (!empty($overviewMissing)) {
            echo "❌ overview 缺少字段: " . implode(', ', $overviewMissing) . "\n";
        }
        if (!$statusStatsValid) {
            echo "❌ statusStats 字段格式不正确\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ getStatistics 方法测试失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 测试3：验证getFilterOptions方法返回的字段格式
echo "3. 测试 getFilterOptions 方法返回字段格式...\n";
try {
    $result = ResumeCancelSearchService::getFilterOptions();
    
    $expectedOptions = ['statusOptions', 'cancelReasonOptions', 'smsStatusOptions'];
    $missingOptions = [];
    
    foreach ($expectedOptions as $option) {
        if (!array_key_exists($option, $result)) {
            $missingOptions[] = $option;
        }
    }
    
    // 检查选项格式
    $optionFormatValid = true;
    if (!empty($result['statusOptions'])) {
        $firstOption = $result['statusOptions'][0];
        if (!array_key_exists('value', $firstOption) || !array_key_exists('label', $firstOption)) {
            $optionFormatValid = false;
        }
    }
    
    if (empty($missingOptions) && $optionFormatValid) {
        echo "✅ getFilterOptions 方法字段格式正确\n";
    } else {
        if (!empty($missingOptions)) {
            echo "❌ 缺少选项: " . implode(', ', $missingOptions) . "\n";
        }
        if (!$optionFormatValid) {
            echo "❌ 选项格式不正确\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ getFilterOptions 方法测试失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 测试4：验证参数格式
echo "4. 测试小驼峰参数格式...\n";
try {
    $camelCaseParams = [
        'cancelLogId' => 1,
        'mobile' => '138',
        'name' => '测试',
        'status' => 1,
        'cancelReasonType' => 1,
        'smsStatus' => 1,
        'applyTimeStart' => '2024-01-01',
        'applyTimeEnd' => '2024-01-31',
        'adminId' => 1,
        'sortField' => 'applyTime',
        'sortOrder' => 'DESC',
        'page' => 1,
        'pageSize' => 5
    ];
    
    $result = ResumeCancelSearchService::getList($camelCaseParams);
    
    if (isset($result['list']) && isset($result['pages'])) {
        echo "✅ 小驼峰参数格式支持正常\n";
    } else {
        echo "❌ 小驼峰参数格式支持异常\n";
    }
    
} catch (Exception $e) {
    echo "❌ 参数格式测试失败: " . $e->getMessage() . "\n";
}

echo "\n=== 验证完成 ===\n";
