<?php

namespace tests\unit\service;

use common\service\memberCancel\ResumeCancelSearchService;
use common\base\models\BaseResumeCancelLog;
use PHPUnit\Framework\TestCase;

/**
 * 求职者注销搜索服务测试
 */
class ResumeCancelSearchServiceTest extends TestCase
{
    /**
     * 测试获取列表方法
     */
    public function testGetList()
    {
        // 测试基本查询
        $params = [
            'page' => 1,
            'pageSize' => 10
        ];
        
        $result = ResumeCancelSearchService::getList($params);
        
        // 验证返回结构
        $this->assertArrayHasKey('list', $result);
        $this->assertArrayHasKey('pages', $result);
        $this->assertArrayHasKey('total', $result['pages']);
        $this->assertArrayHasKey('pageSize', $result['pages']);
        $this->assertArrayHasKey('page', $result['pages']);
        $this->assertArrayHasKey('totalPages', $result['pages']);
    }

    /**
     * 测试获取详情方法
     */
    public function testGetDetail()
    {
        // 创建测试数据
        $cancelLog = new BaseResumeCancelLog();
        $cancelLog->member_id = 1;
        $cancelLog->resume_id = 1;
        $cancelLog->cancel_reason_type = BaseResumeCancelLog::CANCEL_REASON_TYPE_FOUND_JOB;
        $cancelLog->cancel_reason_detail = '测试原因';
        $cancelLog->apply_time = date('Y-m-d H:i:s');
        $cancelLog->cooldown_end_time = date('Y-m-d H:i:s', strtotime('+7 days'));
        $cancelLog->status = BaseResumeCancelLog::STATUS_APPLYING;
        $cancelLog->ip = '127.0.0.1';
        $cancelLog->sms_status = BaseResumeCancelLog::SMS_STATUS_APPLY_SENT;
        
        if ($cancelLog->save()) {
            $result = ResumeCancelSearchService::getDetail($cancelLog->id);
            
            // 验证返回数据
            $this->assertEquals($cancelLog->id, $result['id']);
            $this->assertEquals('申请中', $result['statusText']);
            $this->assertEquals('已找到新工作，以后不再打算找工作', $result['cancelReasonTypeText']);
            $this->assertArrayHasKey('remainingCooldownDays', $result);
            $this->assertArrayHasKey('operationType', $result);
            
            // 清理测试数据
            $cancelLog->delete();
        }
    }

    /**
     * 测试获取统计数据方法
     */
    public function testGetStatistics()
    {
        $params = [
            'startDate' => date('Y-m-01'),
            'endDate' => date('Y-m-d')
        ];
        
        $result = ResumeCancelSearchService::getStatistics($params);
        
        // 验证返回结构
        $this->assertArrayHasKey('overview', $result);
        $this->assertArrayHasKey('statusStats', $result);
        $this->assertArrayHasKey('reasonStats', $result);
        $this->assertArrayHasKey('smsStats', $result);
        
        // 验证概览数据
        $this->assertArrayHasKey('totalApplies', $result['overview']);
        $this->assertArrayHasKey('todayApplies', $result['overview']);
        $this->assertArrayHasKey('weekApplies', $result['overview']);
        $this->assertArrayHasKey('monthApplies', $result['overview']);
        
        // 验证统计数据结构
        foreach ($result['statusStats'] as $stat) {
            $this->assertArrayHasKey('status', $stat);
            $this->assertArrayHasKey('statusName', $stat);
            $this->assertArrayHasKey('count', $stat);
            $this->assertArrayHasKey('percentage', $stat);
        }
    }

    /**
     * 测试获取筛选选项方法
     */
    public function testGetFilterOptions()
    {
        $result = ResumeCancelSearchService::getFilterOptions();
        
        // 验证返回结构
        $this->assertArrayHasKey('statusOptions', $result);
        $this->assertArrayHasKey('cancelReasonOptions', $result);
        $this->assertArrayHasKey('smsStatusOptions', $result);
        
        // 验证选项数据结构
        foreach ($result['statusOptions'] as $option) {
            $this->assertArrayHasKey('value', $option);
            $this->assertArrayHasKey('label', $option);
        }
        
        // 验证状态选项数量
        $this->assertCount(count(BaseResumeCancelLog::STATUS_LIST), $result['statusOptions']);
        $this->assertCount(count(BaseResumeCancelLog::CANCEL_REASON_TYPE_LIST), $result['cancelReasonOptions']);
        $this->assertCount(count(BaseResumeCancelLog::SMS_STATUS_LIST), $result['smsStatusOptions']);
    }

    /**
     * 测试搜索条件
     */
    public function testSearchConditions()
    {
        // 测试手机号搜索
        $params = [
            'mobile' => '138',
            'page' => 1,
            'pageSize' => 10
        ];
        
        $result = ResumeCancelSearchService::getList($params);
        $this->assertIsArray($result['list']);
        
        // 测试状态筛选
        $params = [
            'status' => BaseResumeCancelLog::STATUS_APPLYING,
            'page' => 1,
            'pageSize' => 10
        ];
        
        $result = ResumeCancelSearchService::getList($params);
        $this->assertIsArray($result['list']);
        
        // 测试时间范围筛选
        $params = [
            'applyTimeStart' => date('Y-m-01'),
            'applyTimeEnd' => date('Y-m-d'),
            'page' => 1,
            'pageSize' => 10
        ];
        
        $result = ResumeCancelSearchService::getList($params);
        $this->assertIsArray($result['list']);
    }

    /**
     * 测试排序功能
     */
    public function testSorting()
    {
        $params = [
            'sortField' => 'apply_time',
            'sortOrder' => 'DESC',
            'page' => 1,
            'pageSize' => 10
        ];
        
        $result = ResumeCancelSearchService::getList($params);
        $this->assertIsArray($result['list']);
        
        // 测试其他排序字段
        $sortFields = ['cooldown_end_time', 'complete_time', 'status'];
        foreach ($sortFields as $field) {
            $params['sortField'] = $field;
            $result = ResumeCancelSearchService::getList($params);
            $this->assertIsArray($result['list']);
        }
    }

    /**
     * 测试异常情况
     */
    public function testExceptions()
    {
        // 测试获取不存在的详情
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('注销申请记录不存在');
        
        ResumeCancelSearchService::getDetail(999999);
    }
}
